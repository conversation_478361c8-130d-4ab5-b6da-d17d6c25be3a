{"id": "uni-loading", "displayName": "uni-loading", "version": "1.0.6", "description": "加载动画组件多用在页面内数据加载时，提供一个loading动画，列表的上拉加载，下拉刷新等都需要加载动画", "keywords": ["loading", "加载动画", "上拉刷新", "下拉加载"], "repository": "", "engines": {"HBuilderX": "^3.97"}, "dcloudext": {"type": "component-vue", "sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "无", "permissions": "无"}, "npmurl": ""}, "uni_modules": {"dependencies": ["uni-icons"], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y", "alipay": "y"}, "client": {"Vue": {"vue2": "n", "vue3": "y"}, "App": {"app-vue": "n", "app-nvue": "n", "app-uvue": "y"}, "H5-mobile": {"Safari": "n", "Android Browser": "n", "微信浏览器(Android)": "n", "QQ浏览器(Android)": "n"}, "H5-pc": {"Chrome": "n", "IE": "n", "Edge": "n", "Firefox": "n", "Safari": "n"}, "小程序": {"微信": "n", "阿里": "n", "百度": "n", "字节跳动": "n", "QQ": "n", "钉钉": "n", "快手": "n", "飞书": "n", "京东": "n"}, "快应用": {"华为": "n", "联盟": "n"}}}}}