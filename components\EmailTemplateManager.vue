<template>
  <view class="template-manager">
    <!-- 搜索和操作栏 -->
    <view class="search-bar">
      <view class="search-form">
        <view class="form-row">
          <view class="form-item">
            <text class="label">模板名称:</text>
            <input 
              v-model="searchForm.templateName" 
              placeholder="请输入模板名称"
              class="input"
            />
          </view>
          <view class="form-item">
            <text class="label">邮件类型:</text>
            <picker 
              :value="emailTypeIndex" 
              :range="emailTypeOptions"
              range-key="label"
              @change="onEmailTypeChange"
              class="picker"
            >
              <view class="picker-text">
                {{ emailTypeOptions[emailTypeIndex]?.label || '请选择邮件类型' }}
              </view>
            </picker>
          </view>
          <view class="form-item">
            <text class="label">语言:</text>
            <picker 
              :value="languageIndex" 
              :range="languageOptions"
              range-key="label"
              @change="onLanguageChange"
              class="picker"
            >
              <view class="picker-text">
                {{ languageOptions[languageIndex]?.label || '请选择语言' }}
              </view>
            </picker>
          </view>
        </view>
        <view class="form-row">
          <view class="form-item">
            <text class="label">状态:</text>
            <picker 
              :value="statusIndex" 
              :range="statusOptions"
              range-key="label"
              @change="onStatusChange"
              class="picker"
            >
              <view class="picker-text">
                {{ statusOptions[statusIndex]?.label || '请选择状态' }}
              </view>
            </picker>
          </view>
          <view class="form-actions">
            <button @click="searchTemplates" class="btn btn-primary">
              <text class="btn-icon">🔍</text>
              <text>搜索</text>
            </button>
            <button @click="resetSearch" class="btn btn-secondary">
              <text class="btn-icon">🔄</text>
              <text>重置</text>
            </button>
            <button @click="showAddDialog" class="btn btn-success">
              <text class="btn-icon">➕</text>
              <text>新增模板</text>
            </button>
          </view>
        </view>
      </view>
    </view>

    <!-- 模板列表 -->
    <view class="template-list">
      <view class="list-header">
        <text class="section-title">模板列表</text>
        <view class="list-actions">
          <button @click="exportTemplates" class="btn btn-outline">
            <text class="btn-icon">📤</text>
            <text>导出</text>
          </button>
          <button @click="showImportDialog" class="btn btn-outline">
            <text class="btn-icon">📥</text>
            <text>导入</text>
          </button>
        </view>
      </view>

      <view v-if="loading" class="loading-container">
        <view class="loading-spinner"></view>
        <text class="loading-text">加载中...</text>
      </view>

      <view v-else-if="templateList.length === 0" class="empty-state">
        <view class="empty-icon">📄</view>
        <text class="empty-text">暂无模板数据</text>
        <button @click="showAddDialog" class="btn btn-primary">
          <text>创建第一个模板</text>
        </button>
      </view>

      <view v-else class="template-cards">
        <view 
          v-for="template in templateList" 
          :key="template.id"
          class="template-card"
          :class="{ 'is-default': template.isDefault === 'Y' }"
        >
          <view class="card-header">
            <view class="template-info">
              <text class="template-name">{{ template.templateName }}</text>
              <text class="template-code">{{ template.templateCode }}</text>
            </view>
            <view class="template-badges">
              <view v-if="template.isDefault === 'Y'" class="badge badge-primary">默认</view>
              <view 
                class="badge"
                :class="getStatusClass(template.status)"
              >
                {{ getStatusText(template.status) }}
              </view>
            </view>
          </view>

          <view class="card-content">
            <view class="template-meta">
              <view class="meta-item">
                <text class="meta-label">邮件类型:</text>
                <text class="meta-value">{{ getEmailTypeName(template.emailType) }}</text>
              </view>
              <view class="meta-item">
                <text class="meta-label">语言:</text>
                <text class="meta-value">{{ getLanguageName(template.language) }}</text>
              </view>
              <view class="meta-item">
                <text class="meta-label">创建时间:</text>
                <text class="meta-value">{{ formatDate(template.createTime) }}</text>
              </view>
            </view>

            <view class="template-preview">
              <text class="preview-label">主题预览:</text>
              <text class="preview-text">{{ template.subjectTemplate || '无主题' }}</text>
            </view>
          </view>

          <view class="card-actions">
            <button @click="viewTemplate(template)" class="btn btn-sm btn-outline">
              <text class="btn-icon">👁️</text>
              <text>查看</text>
            </button>
            <button @click="editTemplate(template)" class="btn btn-sm btn-primary">
              <text class="btn-icon">✏️</text>
              <text>编辑</text>
            </button>
            <button @click="copyTemplate(template)" class="btn btn-sm btn-secondary">
              <text class="btn-icon">📋</text>
              <text>复制</text>
            </button>
            <button 
              v-if="template.isDefault !== 'Y'"
              @click="setAsDefault(template)" 
              class="btn btn-sm btn-warning"
            >
              <text class="btn-icon">⭐</text>
              <text>设为默认</text>
            </button>
            <button @click="deleteTemplate(template)" class="btn btn-sm btn-danger">
              <text class="btn-icon">🗑️</text>
              <text>删除</text>
            </button>
          </view>
        </view>
      </view>

      <!-- 分页 -->
      <view v-if="pagination.total > 0" class="pagination">
        <button 
          @click="prevPage" 
          :disabled="pagination.current <= 1"
          class="btn btn-outline btn-sm"
        >
          上一页
        </button>
        <text class="page-info">
          第 {{ pagination.current }} 页，共 {{ pagination.pages }} 页，总计 {{ pagination.total }} 条
        </text>
        <button 
          @click="nextPage" 
          :disabled="pagination.current >= pagination.pages"
          class="btn btn-outline btn-sm"
        >
          下一页
        </button>
      </view>
    </view>

    <!-- 加载提示 -->
    <view v-if="globalLoading" class="global-loading">
      <view class="loading-content">
        <view class="loading-spinner"></view>
        <text class="loading-text">{{ loadingText }}</text>
      </view>
    </view>

    <!-- 模板编辑器 -->
    <EmailTemplateEditor
      :visible="editorVisible"
      :mode="editorMode"
      :templateData="currentTemplate"
      @close="closeEditor"
      @success="onEditorSuccess"
    />
  </view>
</template>

<script>
import {
  getTemplateList,
  deleteTemplate as deleteTemplateApi,
  setDefaultTemplate,
  copyTemplate as copyTemplateApi,
  exportTemplates as exportTemplatesApi
} from '@/api/email.js';
import EmailTemplateEditor from '@/components/EmailTemplateEditor.vue';

export default {
  name: 'EmailTemplateManager',

  components: {
    EmailTemplateEditor
  },

  data() {
    return {
      // 搜索表单
      searchForm: {
        templateName: '',
        emailType: '',
        language: '',
        status: ''
      },

      // 选择器索引
      emailTypeIndex: 0,
      languageIndex: 0,
      statusIndex: 0,

      // 选择器选项
      emailTypeOptions: [
        { label: '全部', value: '' },
        { label: '推送報價1版', value: 'QUOTE_V1' },
        { label: '推送報價2版', value: 'QUOTE_V2' },
        { label: '推送報價3版', value: 'QUOTE_V3' },
        { label: '推送報價4版', value: 'QUOTE_V4' },
        { label: '推送報價5版', value: 'QUOTE_V5' },
        { label: '推送預估Z版', value: 'ESTIMATE_Z' },
        { label: '推送預估ZZ版', value: 'ESTIMATE_ZZ' },
        { label: '推送P版', value: 'P_VERSION' },
        { label: '更新版本邮件提醒', value: 'UPDATE_NOTIFY' }
      ],

      languageOptions: [
        { label: '全部', value: '' },
        { label: '中文简体', value: 'zh_CN' },
        { label: '中文繁体', value: 'zh_TW' },
        { label: '英文', value: 'en_US' }
      ],

      statusOptions: [
        { label: '全部', value: '' },
        { label: '启用', value: 'Y' },
        { label: '禁用', value: 'N' }
      ],

      // 模板列表
      templateList: [],
      
      // 分页信息
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
        pages: 0
      },

      // 加载状态
      loading: false,
      globalLoading: false,
      loadingText: '加载中...',

      // 编辑器状态
      editorVisible: false,
      editorMode: 'add', // add, edit, view
      currentTemplate: {}
    };
  },

  mounted() {
    this.loadTemplates();
  },

  methods: {
    /**
     * 加载模板列表
     */
    async loadTemplates() {
      try {
        this.loading = true;
        
        const params = {
          pageNo: this.pagination.current,
          pageSize: this.pagination.pageSize,
          templateName: this.searchForm.templateName || undefined,
          emailType: this.searchForm.emailType || undefined,
          language: this.searchForm.language || undefined,
          status: this.searchForm.status || undefined
        };

        const response = await getTemplateList(params);
        
        if (response.code === 1 && response.data) {
          this.templateList = response.data.list || [];
          this.pagination = {
            current: response.data.pageNum || 1,
            pageSize: response.data.pageSize || 10,
            total: response.data.total || 0,
            pages: response.data.pages || 0
          };
        } else {
          throw new Error(response.message || '获取模板列表失败');
        }
      } catch (error) {
        console.error('加载模板列表失败:', error);
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },

    /**
     * 搜索模板
     */
    searchTemplates() {
      this.pagination.current = 1;
      this.loadTemplates();
    },

    /**
     * 重置搜索
     */
    resetSearch() {
      this.searchForm = {
        templateName: '',
        emailType: '',
        language: '',
        status: ''
      };
      this.emailTypeIndex = 0;
      this.languageIndex = 0;
      this.statusIndex = 0;
      this.pagination.current = 1;
      this.loadTemplates();
    },

    /**
     * 邮件类型选择变化
     */
    onEmailTypeChange(e) {
      this.emailTypeIndex = e.detail.value;
      this.searchForm.emailType = this.emailTypeOptions[this.emailTypeIndex].value;
    },

    /**
     * 语言选择变化
     */
    onLanguageChange(e) {
      this.languageIndex = e.detail.value;
      this.searchForm.language = this.languageOptions[this.languageIndex].value;
    },

    /**
     * 状态选择变化
     */
    onStatusChange(e) {
      this.statusIndex = e.detail.value;
      this.searchForm.status = this.statusOptions[this.statusIndex].value;
    },

    /**
     * 上一页
     */
    prevPage() {
      if (this.pagination.current > 1) {
        this.pagination.current--;
        this.loadTemplates();
      }
    },

    /**
     * 下一页
     */
    nextPage() {
      if (this.pagination.current < this.pagination.pages) {
        this.pagination.current++;
        this.loadTemplates();
      }
    },

    /**
     * 显示新增对话框
     */
    showAddDialog() {
      this.editorMode = 'add';
      this.currentTemplate = {};
      this.editorVisible = true;
    },

    /**
     * 查看模板
     */
    viewTemplate(template) {
      this.editorMode = 'view';
      this.currentTemplate = template;
      this.editorVisible = true;
    },

    /**
     * 编辑模板
     */
    editTemplate(template) {
      this.editorMode = 'edit';
      this.currentTemplate = template;
      this.editorVisible = true;
    },

    /**
     * 复制模板
     */
    async copyTemplate(template) {
      try {
        const result = await uni.showModal({
          title: '复制模板',
          content: `确定要复制模板"${template.templateName}"吗？`,
          confirmText: '确定',
          cancelText: '取消'
        });

        if (result.confirm) {
          this.globalLoading = true;
          this.loadingText = '复制模板中...';

          const copyData = {
            sourceId: template.id,
            newCode: `${template.templateCode}_copy_${Date.now()}`,
            newName: `${template.templateName}_副本`,
            createUser: 'current_user' // 这里应该从用户信息中获取
          };

          const response = await copyTemplateApi(copyData);

          if (response.code === 1) {
            uni.showToast({
              title: '复制成功',
              icon: 'success'
            });
            this.loadTemplates();
          } else {
            throw new Error(response.message || '复制失败');
          }
        }
      } catch (error) {
        console.error('复制模板失败:', error);
        uni.showToast({
          title: '复制失败',
          icon: 'none'
        });
      } finally {
        this.globalLoading = false;
      }
    },

    /**
     * 设为默认模板
     */
    async setAsDefault(template) {
      try {
        const result = await uni.showModal({
          title: '设为默认模板',
          content: `确定要将"${template.templateName}"设为默认模板吗？`,
          confirmText: '确定',
          cancelText: '取消'
        });

        if (result.confirm) {
          this.globalLoading = true;
          this.loadingText = '设置默认模板中...';

          const response = await setDefaultTemplate(template.id);

          if (response.code === 1) {
            uni.showToast({
              title: '设置成功',
              icon: 'success'
            });
            this.loadTemplates();
          } else {
            throw new Error(response.message || '设置失败');
          }
        }
      } catch (error) {
        console.error('设置默认模板失败:', error);
        uni.showToast({
          title: '设置失败',
          icon: 'none'
        });
      } finally {
        this.globalLoading = false;
      }
    },

    /**
     * 删除模板
     */
    async deleteTemplate(template) {
      try {
        const result = await uni.showModal({
          title: '删除模板',
          content: `确定要删除模板"${template.templateName}"吗？此操作不可恢复！`,
          confirmText: '删除',
          cancelText: '取消'
        });

        if (result.confirm) {
          this.globalLoading = true;
          this.loadingText = '删除模板中...';

          const response = await deleteTemplateApi(template.id);

          if (response.code === 1) {
            uni.showToast({
              title: '删除成功',
              icon: 'success'
            });
            this.loadTemplates();
          } else {
            throw new Error(response.message || '删除失败');
          }
        }
      } catch (error) {
        console.error('删除模板失败:', error);
        uni.showToast({
          title: '删除失败',
          icon: 'none'
        });
      } finally {
        this.globalLoading = false;
      }
    },

    /**
     * 导出模板
     */
    async exportTemplates() {
      try {
        this.globalLoading = true;
        this.loadingText = '导出模板中...';

        const response = await exportTemplatesApi(
          this.searchForm.emailType,
          this.searchForm.language
        );

        if (response.code === 1) {
          // 这里可以处理导出逻辑，比如下载文件
          uni.showToast({
            title: '导出成功',
            icon: 'success'
          });
          console.log('导出的模板数据:', response.data);
        } else {
          throw new Error(response.message || '导出失败');
        }
      } catch (error) {
        console.error('导出模板失败:', error);
        uni.showToast({
          title: '导出失败',
          icon: 'none'
        });
      } finally {
        this.globalLoading = false;
      }
    },

    /**
     * 显示导入对话框
     */
    showImportDialog() {
      this.$emit('import-templates');
    },

    /**
     * 获取邮件类型名称
     */
    getEmailTypeName(emailType) {
      const option = this.emailTypeOptions.find(opt => opt.value === emailType);
      return option ? option.label : emailType;
    },

    /**
     * 获取语言名称
     */
    getLanguageName(language) {
      const option = this.languageOptions.find(opt => opt.value === language);
      return option ? option.label : language;
    },

    /**
     * 获取状态样式类
     */
    getStatusClass(status) {
      return status === 'Y' ? 'badge-success' : 'badge-secondary';
    },

    /**
     * 获取状态文本
     */
    getStatusText(status) {
      return status === 'Y' ? '启用' : '禁用';
    },

    /**
     * 格式化日期
     */
    formatDate(dateStr) {
      if (!dateStr) return '';
      const date = new Date(dateStr);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
    },

    /**
     * 关闭编辑器
     */
    closeEditor() {
      this.editorVisible = false;
      this.currentTemplate = {};
    },

    /**
     * 编辑器成功回调
     */
    onEditorSuccess() {
      this.loadTemplates();
    }
  }
};
</script>

<style scoped>
.template-manager {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 搜索栏样式 */
.search-bar {
  background: white;
  padding: 20px;
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.search-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.form-row {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  align-items: flex-end;
}

.form-item {
  display: flex;
  flex-direction: column;
  min-width: 200px;
  flex: 1;
}

.label {
  font-size: 14px;
  color: #333;
  margin-bottom: 5px;
  font-weight: 500;
}

.input {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  background: white;
}

.input:focus {
  border-color: #007bff;
  outline: none;
}

.picker {
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
}

.picker-text {
  padding: 8px 12px;
  font-size: 14px;
  color: #333;
}

.form-actions {
  display: flex;
  gap: 10px;
  align-items: flex-end;
}

/* 按钮样式 */
.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  gap: 5px;
  white-space: nowrap;
}

.btn-primary {
  background-color: #007bff;
  color: white;
}

.btn-primary:hover {
  background-color: #0056b3;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-success {
  background-color: #28a745;
  color: white;
}

.btn-warning {
  background-color: #ffc107;
  color: #212529;
}

.btn-danger {
  background-color: #dc3545;
  color: white;
}

.btn-outline {
  background-color: transparent;
  color: #007bff;
  border: 1px solid #007bff;
}

.btn-outline:hover {
  background-color: #007bff;
  color: white;
}

.btn-sm {
  padding: 4px 8px;
  font-size: 12px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-icon {
  font-size: 16px;
}

/* 模板列表样式 */
.template-list {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid #007bff;
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.list-actions {
  display: flex;
  gap: 10px;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 14px;
  color: #666;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 20px;
  opacity: 0.5;
}

.empty-text {
  font-size: 16px;
  color: #666;
  margin-bottom: 20px;
}

/* 模板卡片 */
.template-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
}

.template-card {
  border: 1px solid #eee;
  border-radius: 8px;
  background: white;
  transition: all 0.3s;
  overflow: hidden;
}

.template-card:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  transform: translateY(-2px);
}

.template-card.is-default {
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0,123,255,0.1);
}

.card-header {
  padding: 15px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.template-info {
  flex: 1;
}

.template-name {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 5px;
}

.template-code {
  font-size: 12px;
  color: #666;
  font-family: monospace;
  background: #e9ecef;
  padding: 2px 6px;
  border-radius: 3px;
}

.template-badges {
  display: flex;
  gap: 5px;
  flex-wrap: wrap;
}

.badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  white-space: nowrap;
}

.badge-primary {
  background-color: #007bff;
  color: white;
}

.badge-success {
  background-color: #28a745;
  color: white;
}

.badge-secondary {
  background-color: #6c757d;
  color: white;
}

.card-content {
  padding: 15px 20px;
}

.template-meta {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 15px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.meta-label {
  font-size: 12px;
  color: #666;
  min-width: 60px;
}

.meta-value {
  font-size: 12px;
  color: #333;
  flex: 1;
}

.template-preview {
  background: #f8f9fa;
  padding: 10px;
  border-radius: 4px;
  border-left: 3px solid #007bff;
}

.preview-label {
  font-size: 12px;
  color: #666;
  display: block;
  margin-bottom: 5px;
}

.preview-text {
  font-size: 13px;
  color: #333;
  line-height: 1.4;
  word-break: break-all;
}

.card-actions {
  padding: 15px 20px;
  background: #f8f9fa;
  border-top: 1px solid #eee;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* 分页样式 */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 15px;
  margin-top: 30px;
  padding: 20px;
}

.page-info {
  font-size: 14px;
  color: #666;
}

/* 全局加载 */
.global-loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 3000;
}

.loading-content {
  background: white;
  padding: 30px 40px;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0,0,0,0.2);
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-bar {
    padding: 15px;
  }

  .form-row {
    flex-direction: column;
    gap: 10px;
  }

  .form-item {
    min-width: auto;
  }

  .form-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .template-cards {
    grid-template-columns: 1fr;
  }

  .card-actions {
    flex-direction: column;
  }

  .pagination {
    flex-direction: column;
    gap: 10px;
  }
}
</style>
