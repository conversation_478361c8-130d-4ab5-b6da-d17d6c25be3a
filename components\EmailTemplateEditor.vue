<template>
  <view class="template-editor">
    <!-- 模态框遮罩 -->
    <view v-if="visible" class="modal-overlay" @click="closeModal">
      <view class="modal-content" @click.stop>
        <!-- 模态框头部 -->
        <view class="modal-header">
          <text class="modal-title">
            {{ mode === 'add' ? '新增模板' : mode === 'edit' ? '编辑模板' : '查看模板' }}
          </text>
          <button @click="closeModal" class="close-btn">
            <text class="close-icon">×</text>
          </button>
        </view>

        <!-- 模态框内容 -->
        <view class="modal-body">
          <form class="template-form">
            <!-- 基本信息 -->
            <view class="form-section">
              <text class="section-title">基本信息</text>
              
              <view class="form-row">
                <view class="form-item">
                  <text class="label">模板名称 <text class="required">*</text></text>
                  <input 
                    v-model="formData.templateName"
                    :disabled="mode === 'view'"
                    placeholder="请输入模板名称"
                    class="input"
                  />
                </view>
                <view class="form-item">
                  <text class="label">模板代码 <text class="required">*</text></text>
                  <input 
                    v-model="formData.templateCode"
                    :disabled="mode === 'view' || mode === 'edit'"
                    placeholder="请输入模板代码"
                    class="input"
                  />
                </view>
              </view>

              <view class="form-row">
                <view class="form-item">
                  <text class="label">邮件类型 <text class="required">*</text></text>
                  <picker 
                    :value="emailTypeIndex" 
                    :range="emailTypeOptions"
                    :disabled="mode === 'view'"
                    range-key="label"
                    @change="onEmailTypeChange"
                    class="picker"
                  >
                    <view class="picker-text">
                      {{ emailTypeOptions[emailTypeIndex]?.label || '请选择邮件类型' }}
                    </view>
                  </picker>
                </view>
                <view class="form-item">
                  <text class="label">语言 <text class="required">*</text></text>
                  <picker 
                    :value="languageIndex" 
                    :range="languageOptions"
                    :disabled="mode === 'view'"
                    range-key="label"
                    @change="onLanguageChange"
                    class="picker"
                  >
                    <view class="picker-text">
                      {{ languageOptions[languageIndex]?.label || '请选择语言' }}
                    </view>
                  </picker>
                </view>
              </view>

              <view class="form-row">
                <view class="form-item">
                  <text class="label">状态</text>
                  <picker 
                    :value="statusIndex" 
                    :range="statusOptions"
                    :disabled="mode === 'view'"
                    range-key="label"
                    @change="onStatusChange"
                    class="picker"
                  >
                    <view class="picker-text">
                      {{ statusOptions[statusIndex]?.label || '请选择状态' }}
                    </view>
                  </picker>
                </view>
                <view class="form-item">
                  <text class="label">是否默认</text>
                  <picker
                    :value="defaultIndex"
                    :range="defaultOptions"
                    :disabled="mode === 'view'"
                    range-key="label"
                    @change="onDefaultChange"
                    class="picker"
                  >
                    <view class="picker-text">
                      {{ defaultOptions[defaultIndex]?.label || '请选择是否默认' }}
                    </view>
                  </picker>
                </view>
              </view>
            </view>

            <!-- 模板内容 -->
            <view class="form-section">
              <text class="section-title">模板内容</text>

              <view class="template-content-area">
                <view class="form-item full-width">
                  <text class="label">邮件主题模板</text>
                  <textarea
                    v-model="formData.subjectTemplate"
                    :disabled="mode === 'view'"
                    placeholder="请输入邮件主题模板，支持变量如：${customerName}"
                    class="textarea"
                    rows="2"
                  />
                </view>

                <view class="form-item full-width">
                  <text class="label">邮件内容模板</text>
                  <textarea
                    v-model="formData.contentTemplate"
                    :disabled="mode === 'view'"
                    placeholder="请输入邮件内容模板，支持HTML和变量"
                    class="textarea large"
                    rows="10"
                  />
                </view>
              </view>
            </view>

            <!-- 变量说明 -->
            <view class="form-section">
              <text class="section-title">可用变量</text>
              <view class="variables-help">
                <text class="help-text">
                  模板中可以使用以下变量（使用 ${变量名} 格式）：
                </text>
                <view class="variable-list">
                  <text class="variable-item">• ${customerName} - 客户名称</text>
                  <text class="variable-item">• ${businessId} - 业务ID</text>
                  <text class="variable-item">• ${quoteVersion} - 报价版本</text>
                  <text class="variable-item">• ${createTime} - 创建时间</text>
                  <text class="variable-item">• ${updateTime} - 更新时间</text>
                </view>
              </view>
            </view>
          </form>
        </view>

        <!-- 模态框底部 -->
        <view class="modal-footer">
          <button @click="closeModal" class="btn btn-secondary">
            {{ mode === 'view' ? '关闭' : '取消' }}
          </button>
          <button 
            v-if="mode !== 'view'"
            @click="validateTemplate" 
            class="btn btn-outline"
            :disabled="loading"
          >
            验证模板
          </button>
          <button 
            v-if="mode !== 'view'"
            @click="saveTemplate" 
            class="btn btn-primary"
            :disabled="loading"
          >
            {{ loading ? '保存中...' : '保存' }}
          </button>
        </view>
      </view>
    </view>

    <!-- 加载提示 -->
    <view v-if="loading" class="loading-overlay">
      <view class="loading-content">
        <view class="loading-spinner"></view>
        <text class="loading-text">{{ loadingText }}</text>
      </view>
    </view>
  </view>
</template>

<script>
import { 
  addTemplate, 
  updateTemplate, 
  validateTemplate as validateTemplateApi 
} from '@/api/email.js';

export default {
  name: 'EmailTemplateEditor',
  
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    mode: {
      type: String,
      default: 'add', // add, edit, view
      validator: value => ['add', 'edit', 'view'].includes(value)
    },
    templateData: {
      type: Object,
      default: () => ({})
    }
  },

  data() {
    return {
      // 表单数据
      formData: {
        id: null,
        templateName: '',
        templateCode: '',
        emailType: '',
        language: 'zh_CN',
        status: 'Y',
        isDefault: 'N',
        subjectTemplate: '',
        contentTemplate: ''
      },

      // 选择器索引
      emailTypeIndex: 0,
      languageIndex: 0,
      statusIndex: 0,
      defaultIndex: 0,

      // 选择器选项
      emailTypeOptions: [
        { label: '推送報價1版', value: 'QUOTE_V1' },
        { label: '推送報價2版', value: 'QUOTE_V2' },
        { label: '推送報價3版', value: 'QUOTE_V3' },
        { label: '推送報價4版', value: 'QUOTE_V4' },
        { label: '推送報價5版', value: 'QUOTE_V5' },
        { label: '推送預估Z版', value: 'ESTIMATE_Z' },
        { label: '推送預估ZZ版', value: 'ESTIMATE_ZZ' },
        { label: '推送P版', value: 'P_VERSION' },
        { label: '更新版本邮件提醒', value: 'UPDATE_NOTIFY' }
      ],

      languageOptions: [
        { label: '中文简体', value: 'zh_CN' },
        { label: '中文繁体', value: 'zh_TW' },
        { label: '英文', value: 'en_US' }
      ],

      statusOptions: [
        { label: '启用', value: 'Y' },
        { label: '禁用', value: 'N' }
      ],

      defaultOptions: [
        { label: '否', value: 'N' },
        { label: '是', value: 'Y' }
      ],

      // 加载状态
      loading: false,
      loadingText: '处理中...'
    };
  },

  watch: {
    visible(newVal) {
      if (newVal) {
        this.initForm();
      }
    },

    templateData: {
      handler() {
        if (this.visible) {
          this.initForm();
        }
      },
      deep: true
    }
  },

  methods: {
    /**
     * 初始化表单
     */
    initForm() {
      if (this.mode === 'add') {
        this.formData = {
          id: null,
          templateName: '',
          templateCode: '',
          emailType: '',
          language: 'zh_CN',
          status: 'Y',
          isDefault: 'N',
          subjectTemplate: '',
          contentTemplate: ''
        };
        this.emailTypeIndex = 0;
        this.languageIndex = 0;
        this.statusIndex = 0;
        this.defaultIndex = 0;
      } else {
        // 编辑或查看模式
        this.formData = { ...this.templateData };
        this.updateSelectIndexes();
      }
    },

    /**
     * 更新选择器索引
     */
    updateSelectIndexes() {
      this.emailTypeIndex = this.emailTypeOptions.findIndex(
        opt => opt.value === this.formData.emailType
      );
      this.languageIndex = this.languageOptions.findIndex(
        opt => opt.value === this.formData.language
      );
      this.statusIndex = this.statusOptions.findIndex(
        opt => opt.value === this.formData.status
      );
      this.defaultIndex = this.defaultOptions.findIndex(
        opt => opt.value === this.formData.isDefault
      );

      if (this.emailTypeIndex === -1) this.emailTypeIndex = 0;
      if (this.languageIndex === -1) this.languageIndex = 0;
      if (this.statusIndex === -1) this.statusIndex = 0;
      if (this.defaultIndex === -1) this.defaultIndex = 0;
    },

    /**
     * 邮件类型选择变化
     */
    onEmailTypeChange(e) {
      this.emailTypeIndex = e.detail.value;
      this.formData.emailType = this.emailTypeOptions[this.emailTypeIndex].value;
    },

    /**
     * 语言选择变化
     */
    onLanguageChange(e) {
      this.languageIndex = e.detail.value;
      this.formData.language = this.languageOptions[this.languageIndex].value;
    },

    /**
     * 状态选择变化
     */
    onStatusChange(e) {
      this.statusIndex = e.detail.value;
      this.formData.status = this.statusOptions[this.statusIndex].value;
    },

    /**
     * 默认状态变化
     */
    onDefaultChange(e) {
      this.defaultIndex = e.detail.value;
      this.formData.isDefault = this.defaultOptions[this.defaultIndex].value;
    },

    /**
     * 关闭模态框
     */
    closeModal() {
      this.$emit('close');
    },

    /**
     * 验证表单
     */
    validateForm() {
      if (!this.formData.templateName.trim()) {
        uni.showToast({
          title: '请输入模板名称',
          icon: 'none'
        });
        return false;
      }

      if (!this.formData.templateCode.trim()) {
        uni.showToast({
          title: '请输入模板代码',
          icon: 'none'
        });
        return false;
      }

      if (!this.formData.emailType) {
        uni.showToast({
          title: '请选择邮件类型',
          icon: 'none'
        });
        return false;
      }

      return true;
    },

    /**
     * 验证模板语法
     */
    async validateTemplate() {
      if (!this.validateForm()) {
        return;
      }

      try {
        this.loading = true;
        this.loadingText = '验证模板中...';

        const validateRequest = {
          subjectTemplate: this.formData.subjectTemplate,
          contentTemplate: this.formData.contentTemplate,
          variables: {
            customerName: '测试客户',
            businessId: 'TEST001',
            quoteVersion: 'V1',
            createTime: new Date().toISOString(),
            updateTime: new Date().toISOString()
          }
        };

        const response = await validateTemplateApi(validateRequest);

        if (response.code === 1) {
          uni.showToast({
            title: '模板验证通过',
            icon: 'success'
          });
        } else {
          throw new Error(response.message || '模板验证失败');
        }
      } catch (error) {
        console.error('验证模板失败:', error);
        uni.showToast({
          title: error.message || '验证失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },

    /**
     * 保存模板
     */
    async saveTemplate() {
      if (!this.validateForm()) {
        return;
      }

      try {
        this.loading = true;
        this.loadingText = this.mode === 'add' ? '创建模板中...' : '更新模板中...';

        const templateData = {
          ...this.formData,
          createUser: 'current_user', // 这里应该从用户信息中获取
          updateUser: 'current_user'
        };

        let response;
        if (this.mode === 'add') {
          response = await addTemplate(templateData);
        } else {
          response = await updateTemplate(templateData);
        }

        if (response.code === 1) {
          uni.showToast({
            title: this.mode === 'add' ? '创建成功' : '更新成功',
            icon: 'success'
          });
          this.$emit('success', templateData);
          this.closeModal();
        } else {
          throw new Error(response.message || '保存失败');
        }
      } catch (error) {
        console.error('保存模板失败:', error);
        uni.showToast({
          title: error.message || '保存失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    }
  }
};
</script>

<style scoped>
.template-editor {
  position: relative;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  padding: 20px;
}

.modal-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.3);
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
}

.modal-header {
  padding: 20px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.modal-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  flex: 1;
}

.close-btn {
  background: none;
  border: none;
  font-size: 20px;
  color: #666;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.3s;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  margin-left: 15px;
}

.close-btn:hover {
  background-color: #f5f5f5;
  color: #333;
}

.close-icon {
  font-size: 24px;
  line-height: 1;
  font-weight: normal;
}

.modal-body {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.modal-footer {
  padding: 20px;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 表单样式 */
.template-form {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.form-section {
  border: 1px solid #eee;
  border-radius: 6px;
  padding: 20px;
  background: #fafafa;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 15px;
  display: block;
  border-bottom: 2px solid #007bff;
  padding-bottom: 8px;
}

.form-row {
  display: flex;
  gap: 20px;
  margin-bottom: 15px;
}

.form-row:last-child {
  margin-bottom: 0;
}

.form-item {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.form-item.full-width {
  flex: none;
  width: 100%;
}

.template-content-area {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.label {
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
  font-weight: 500;
}

.required {
  color: #dc3545;
}

.input, .textarea {
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  background: white;
  transition: border-color 0.3s;
}

.input:focus, .textarea:focus {
  border-color: #007bff;
  outline: none;
}

.input:disabled, .textarea:disabled {
  background-color: #f5f5f5;
  color: #666;
}

.textarea {
  resize: vertical;
  min-height: 80px;
  font-family: inherit;
  width: 100%;
  box-sizing: border-box;
}

.textarea.large {
  min-height: 200px;
}

.picker {
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
}

.picker-text {
  padding: 10px 12px;
  font-size: 14px;
  color: #333;
}

/* 变量帮助 */
.variables-help {
  background: white;
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.help-text {
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
  display: block;
}

.variable-list {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.variable-item {
  font-size: 13px;
  color: #333;
  font-family: monospace;
  background: #f8f9fa;
  padding: 5px 8px;
  border-radius: 3px;
}

/* 按钮样式 */
.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s;
  white-space: nowrap;
}

.btn-primary {
  background-color: #007bff;
  color: white;
}

.btn-primary:hover {
  background-color: #0056b3;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-outline {
  background-color: transparent;
  color: #007bff;
  border: 1px solid #007bff;
}

.btn-outline:hover {
  background-color: #007bff;
  color: white;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 加载状态 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255,255,255,0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.loading-spinner {
  width: 30px;
  height: 30px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 14px;
  color: #666;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .modal-overlay {
    padding: 10px;
  }

  .modal-content {
    max-height: 95vh;
  }

  .form-row {
    flex-direction: column;
    gap: 15px;
  }

  .modal-footer {
    flex-direction: column;
    gap: 10px;
  }

  .btn {
    width: 100%;
  }
}
</style>
