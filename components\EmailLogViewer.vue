<template>
  <view class="email-log-viewer">
    <!-- 搜索区域 -->
    <view class="search-section">
      <view class="section-header">
        <text class="section-title">邮件日志查询</text>
      </view>
      
      <view class="search-form">
        <view class="form-row">
          <view class="form-item">
            <text class="label">邮件类型:</text>
            <picker 
              :value="emailTypeIndex" 
              :range="emailTypeOptions" 
              @change="onEmailTypeChange"
            >
              <view class="picker-text">
                {{ getEmailTypeName(searchForm.emailType) }}
              </view>
            </picker>
          </view>
          
          <view class="form-item">
            <text class="label">发送状态:</text>
            <picker 
              :value="statusIndex" 
              :range="statusOptions" 
              @change="onStatusChange"
            >
              <view class="picker-text">
                {{ getStatusName(searchForm.sendStatus) }}
              </view>
            </picker>
          </view>
          
          <view class="form-item">
            <text class="label">业务ID:</text>
            <input 
              v-model="searchForm.businessId" 
              placeholder="请输入业务ID" 
              class="input"
            />
          </view>
        </view>
        
        <view class="form-row">
          <view class="form-item">
            <text class="label">开始日期:</text>
            <picker 
              mode="date" 
              :value="searchForm.startDate" 
              @change="onStartDateChange"
            >
              <view class="picker-text">
                {{ searchForm.startDate || '请选择开始日期' }}
              </view>
            </picker>
          </view>
          
          <view class="form-item">
            <text class="label">结束日期:</text>
            <picker 
              mode="date" 
              :value="searchForm.endDate" 
              @change="onEndDateChange"
            >
              <view class="picker-text">
                {{ searchForm.endDate || '请选择结束日期' }}
              </view>
            </picker>
          </view>
          
          <view class="form-actions">
            <button @click="searchLogs" class="btn btn-primary">查询</button>
            <button @click="resetSearch" class="btn btn-secondary">重置</button>
            <button @click="exportLogs" class="btn btn-success">导出</button>
          </view>
        </view>
      </view>
    </view>

    <!-- 统计信息 -->
    <view class="statistics-section">
      <view class="section-header">
        <text class="section-title">发送统计</text>
        <button @click="refreshStatistics" class="btn btn-refresh">刷新统计</button>
      </view>
      
      <view class="stats-grid">
        <view class="stat-card">
          <text class="stat-number">{{ statistics.total || 0 }}</text>
          <text class="stat-label">总发送量</text>
        </view>
        <view class="stat-card success">
          <text class="stat-number">{{ statistics.success || 0 }}</text>
          <text class="stat-label">发送成功</text>
        </view>
        <view class="stat-card error">
          <text class="stat-number">{{ statistics.failed || 0 }}</text>
          <text class="stat-label">发送失败</text>
        </view>
        <view class="stat-card pending">
          <text class="stat-number">{{ statistics.pending || 0 }}</text>
          <text class="stat-label">待发送</text>
        </view>
        <view class="stat-card">
          <text class="stat-number">{{ successRate }}%</text>
          <text class="stat-label">成功率</text>
        </view>
      </view>
    </view>

    <!-- 日志列表 -->
    <view class="logs-section">
      <view class="section-header">
        <text class="section-title">日志列表</text>
        <text class="total-count">共 {{ total }} 条记录</text>
      </view>
      
      <!-- 使用 uni-table 组件 -->
      <uni-table
        ref="table"
        :loading="loading"
        border
        stripe
        emptyText="暂无更多数据"
      >
        <!-- 表头 -->
        <uni-tr>
          <uni-th width="100" align="center">日志ID</uni-th>
          <uni-th width="120" align="center">邮件类型</uni-th>
          <uni-th width="120" align="center">业务ID</uni-th>
          <uni-th width="200" align="center">收件人</uni-th>
          <uni-th width="100" align="center">发送状态</uni-th>
          <uni-th width="160" align="center">发送时间</uni-th>
          <uni-th width="100" align="center">发送用户</uni-th>
          <uni-th width="200" align="center">错误信息</uni-th>
          <uni-th width="150" align="center">操作</uni-th>
        </uni-tr>

        <!-- 表格数据行 -->
        <uni-tr v-for="log in logList" :key="log.id">
          <uni-td align="center">{{ log.id }}</uni-td>
          <uni-td align="center">
            <text class="email-type">{{ getEmailTypeName(log.emailType) }}</text>
          </uni-td>
          <uni-td align="center">{{ log.businessId }}</uni-td>
          <uni-td align="center">{{ log.recipients }}</uni-td>
          <uni-td align="center">
            <text :class="getStatusClass(log.sendStatus)">
              {{ getStatusName(log.sendStatus) }}
            </text>
          </uni-td>
          <uni-td align="center">{{ formatDate(log.sendTime) }}</uni-td>
          <uni-td align="center">{{ log.sendUser }}</uni-td>
          <uni-td align="center">
            <text v-if="log.errorMessage" class="error-message">
              {{ log.errorMessage }}
            </text>
            <text v-else>-</text>
          </uni-td>
          <uni-td align="center">
            <view class="action-buttons">
              <button
                v-if="log.sendStatus === 'FAILED'"
                @click="retryEmail(log.id)"
                class="btn btn-retry"
              >
                重试
              </button>
              <button @click="viewDetail(log)" class="btn btn-detail">详情</button>
            </view>
          </uni-td>
        </uni-tr>
      </uni-table>
      
      <!-- 分页 -->
      <view class="pagination">
        <button 
          @click="prevPage" 
          :disabled="currentPage <= 1"
          class="btn btn-page"
        >
          上一页
        </button>
        <text class="page-info">
          第 {{ currentPage }} 页 / 共 {{ totalPages }} 页
        </text>
        <button 
          @click="nextPage" 
          :disabled="currentPage >= totalPages"
          class="btn btn-page"
        >
          下一页
        </button>
      </view>
    </view>

    <!-- 日志详情弹窗 -->
    <view v-if="showDetailDialog" class="dialog-overlay" @click="closeDetailDialog">
      <view class="dialog" @click.stop>
        <view class="dialog-header">
          <text class="dialog-title">日志详情</text>
          <text class="dialog-close" @click="closeDetailDialog">×</text>
        </view>
        
        <view class="dialog-body">
          <view class="detail-grid">
            <view class="detail-item">
              <text class="detail-label">日志ID:</text>
              <text class="detail-value">{{ selectedLog.id }}</text>
            </view>
            <view class="detail-item">
              <text class="detail-label">邮件类型:</text>
              <text class="detail-value">{{ getEmailTypeName(selectedLog.emailType) }}</text>
            </view>
            <view class="detail-item">
              <text class="detail-label">业务ID:</text>
              <text class="detail-value">{{ selectedLog.businessId }}</text>
            </view>
            <view class="detail-item">
              <text class="detail-label">收件人:</text>
              <text class="detail-value">{{ selectedLog.recipients }}</text>
            </view>
            <view class="detail-item">
              <text class="detail-label">发送状态:</text>
              <text :class="['detail-value', getStatusClass(selectedLog.sendStatus)]">
                {{ getStatusName(selectedLog.sendStatus) }}
              </text>
            </view>
            <view class="detail-item">
              <text class="detail-label">发送时间:</text>
              <text class="detail-value">{{ formatDate(selectedLog.sendTime) }}</text>
            </view>
            <view class="detail-item">
              <text class="detail-label">发送用户:</text>
              <text class="detail-value">{{ selectedLog.sendUser }}</text>
            </view>
            <view class="detail-item">
              <text class="detail-label">重试次数:</text>
              <text class="detail-value">{{ selectedLog.retryCount || 0 }}</text>
            </view>
            <view v-if="selectedLog.errorMessage" class="detail-item full-width">
              <text class="detail-label">错误信息:</text>
              <text class="detail-value error">{{ selectedLog.errorMessage }}</text>
            </view>
            <view v-if="selectedLog.emailContent" class="detail-item full-width">
              <text class="detail-label">邮件内容:</text>
              <view class="email-content">{{ selectedLog.emailContent }}</view>
            </view>
          </view>
        </view>
        
        <view class="dialog-footer">
          <button 
            v-if="selectedLog.sendStatus === 'FAILED'" 
            @click="retryEmailFromDetail" 
            class="btn btn-retry"
          >
            重试发送
          </button>
          <button @click="closeDetailDialog" class="btn btn-secondary">关闭</button>
        </view>
      </view>
    </view>

    <!-- 加载提示 -->
    <view v-if="loading" class="loading-overlay">
      <view class="loading-content">
        <text class="loading-text">{{ loadingText }}</text>
      </view>
    </view>
  </view>
</template>

<script>
import {
  getEmailLogs,
  getEmailStatistics,
  retryEmail,
  EMAIL_TYPE_NAMES,
  SEND_STATUS_NAMES
} from '@/api/email.js';

export default {
  name: 'EmailLogViewer',
  data() {
    return {
      // 搜索表单
      searchForm: {
        emailType: '',
        sendStatus: '',
        businessId: '',
        startDate: '',
        endDate: ''
      },
      
      // 日志列表
      logList: [],
      total: 0,
      currentPage: 1,
      pageSize: 10,
      totalPages: 0,
      
      // 统计信息
      statistics: {
        total: 0,
        success: 0,
        failed: 0,
        pending: 0
      },
      
      // 选项数据
      emailTypeOptions: ['全部', ...Object.values(EMAIL_TYPE_NAMES)],
      statusOptions: ['全部', ...Object.values(SEND_STATUS_NAMES)],
      
      // 详情弹窗
      showDetailDialog: false,
      selectedLog: {},
      
      // 加载状态
      loading: false,
      loadingText: '加载中...'
    };
  },
  
  computed: {
    /**
     * 邮件类型索引
     */
    emailTypeIndex() {
      if (!this.searchForm.emailType) return 0;
      const types = Object.keys(EMAIL_TYPE_NAMES);
      return types.indexOf(this.searchForm.emailType) + 1;
    },
    
    /**
     * 状态索引
     */
    statusIndex() {
      if (!this.searchForm.sendStatus) return 0;
      const statuses = Object.keys(SEND_STATUS_NAMES);
      return statuses.indexOf(this.searchForm.sendStatus) + 1;
    },
    
    /**
     * 成功率
     */
    successRate() {
      if (this.statistics.total === 0) return 0;
      return Math.round((this.statistics.success / this.statistics.total) * 100);
    }
  },
  
  mounted() {
    this.initDateRange();
    this.loadLogs();
    this.loadStatistics();
  },
  
  methods: {
    /**
     * 初始化日期范围
     */
    initDateRange() {
      const today = new Date();
      const lastWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
      
      this.searchForm.startDate = this.formatDateForPicker(lastWeek);
      this.searchForm.endDate = this.formatDateForPicker(today);
    },
    
    /**
     * 格式化日期为选择器格式
     */
    formatDateForPicker(date) {
      return date.toISOString().split('T')[0];
    },
    
    /**
     * 加载日志列表
     */
    async loadLogs() {
      try {
        this.loading = true;
        this.loadingText = '加载日志中...';
        
        const params = {
          pageNo: this.currentPage,
          pageSize: this.pageSize,
          ...this.searchForm
        };
        
        // 过滤空值
        Object.keys(params).forEach(key => {
          if (!params[key]) {
            delete params[key];
          }
        });
        
        const response = await getEmailLogs(params);
        if (response.code === 1) {
          this.logList = response.data.records || [];
          this.total = response.data.total || 0;
          this.totalPages = Math.ceil(this.total / this.pageSize);
        } else {
          uni.showToast({
            title: response.message || '加载失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('加载日志失败:', error);
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },
    
    /**
     * 加载统计信息
     */
    async loadStatistics() {
      try {
        const params = {
          startDate: this.searchForm.startDate,
          endDate: this.searchForm.endDate
        };
        
        if (this.searchForm.emailType) {
          params.emailType = this.searchForm.emailType;
        }
        
        const response = await getEmailStatistics(params);
        if (response.code === 1) {
          this.statistics = response.data || {};
        }
      } catch (error) {
        console.error('加载统计失败:', error);
      }
    },
    
    /**
     * 搜索日志
     */
    searchLogs() {
      this.currentPage = 1;
      this.loadLogs();
      this.loadStatistics();
    },
    
    /**
     * 重置搜索
     */
    resetSearch() {
      this.searchForm = {
        emailType: '',
        sendStatus: '',
        businessId: '',
        startDate: '',
        endDate: ''
      };
      this.initDateRange();
      this.currentPage = 1;
      this.loadLogs();
      this.loadStatistics();
    },
    
    /**
     * 刷新统计
     */
    refreshStatistics() {
      this.loadStatistics();
    },
    
    /**
     * 导出日志
     */
    exportLogs() {
      uni.showToast({
        title: '导出功能开发中',
        icon: 'none'
      });
    },
    
    /**
     * 上一页
     */
    prevPage() {
      if (this.currentPage > 1) {
        this.currentPage--;
        this.loadLogs();
      }
    },
    
    /**
     * 下一页
     */
    nextPage() {
      if (this.currentPage < this.totalPages) {
        this.currentPage++;
        this.loadLogs();
      }
    },
    
    /**
     * 重试邮件
     */
    async retryEmail(logId) {
      try {
        this.loading = true;
        this.loadingText = '重试发送中...';
        
        const response = await retryEmail(logId);
        if (response.code === 1) {
          uni.showToast({
            title: '重试成功',
            icon: 'success'
          });
          this.loadLogs();
        } else {
          uni.showToast({
            title: response.message || '重试失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('重试失败:', error);
        uni.showToast({
          title: '重试失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },
    
    /**
     * 查看详情
     */
    viewDetail(log) {
      this.selectedLog = log;
      this.showDetailDialog = true;
    },
    
    /**
     * 关闭详情弹窗
     */
    closeDetailDialog() {
      this.showDetailDialog = false;
      this.selectedLog = {};
    },
    
    /**
     * 从详情重试邮件
     */
    async retryEmailFromDetail() {
      await this.retryEmail(this.selectedLog.id);
      this.closeDetailDialog();
    },
    
    /**
     * 邮件类型选择变化
     */
    onEmailTypeChange(event) {
      const index = event.detail.value;
      if (index === 0) {
        this.searchForm.emailType = '';
      } else {
        const types = Object.keys(EMAIL_TYPE_NAMES);
        this.searchForm.emailType = types[index - 1];
      }
    },
    
    /**
     * 状态选择变化
     */
    onStatusChange(event) {
      const index = event.detail.value;
      if (index === 0) {
        this.searchForm.sendStatus = '';
      } else {
        const statuses = Object.keys(SEND_STATUS_NAMES);
        this.searchForm.sendStatus = statuses[index - 1];
      }
    },
    
    /**
     * 开始日期变化
     */
    onStartDateChange(event) {
      this.searchForm.startDate = event.detail.value;
    },
    
    /**
     * 结束日期变化
     */
    onEndDateChange(event) {
      this.searchForm.endDate = event.detail.value;
    },
    
    /**
     * 获取邮件类型名称
     */
    getEmailTypeName(type) {
      return EMAIL_TYPE_NAMES[type] || type || '全部';
    },
    
    /**
     * 获取状态名称
     */
    getStatusName(status) {
      return SEND_STATUS_NAMES[status] || status || '全部';
    },
    
    /**
     * 获取状态样式类
     */
    getStatusClass(status) {
      return {
        'status-success': status === 'SUCCESS',
        'status-failed': status === 'FAILED',
        'status-pending': status === 'PENDING',
        'status-sending': status === 'SENDING',
        'status-retry': status === 'RETRY'
      };
    },
    
    /**
     * 格式化日期
     */
    formatDate(dateStr) {
      if (!dateStr) return '-';
      const date = new Date(dateStr);
      return date.toLocaleString();
    }
  }
};
</script>

<style scoped>
.email-log-viewer {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 通用样式 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #007bff;
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s;
}

.btn-primary {
  background-color: #007bff;
  color: white;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-success {
  background-color: #28a745;
  color: white;
}

.btn-refresh {
  background-color: #17a2b8;
  color: white;
  padding: 6px 12px;
  font-size: 12px;
}

.btn-retry {
  background-color: #ffc107;
  color: #212529;
  padding: 4px 8px;
  font-size: 12px;
}

.btn-detail {
  background-color: #6f42c1;
  color: white;
  padding: 4px 8px;
  font-size: 12px;
}

/* 搜索区域 */
.search-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.search-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.form-row {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.form-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.label {
  font-size: 14px;
  color: #333;
  white-space: nowrap;
  font-weight: 500;
}

.input {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  width: 200px;
}

.picker-text {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
  font-size: 14px;
  min-width: 120px;
  text-align: center;
  color: #333;
}

.form-actions {
  display: flex;
  gap: 10px;
}

/* 统计区域 */
.statistics-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.stat-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  color: white;
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.stat-card.success {
  background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
}

.stat-card.error {
  background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
}

.stat-card.pending {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-number {
  display: block;
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.9;
}

/* 日志列表 */
.logs-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.total-count {
  font-size: 14px;
  color: #666;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 5px;
  justify-content: center;
  align-items: center;
}

/* 状态样式 */
.status-success {
  color: #28a745;
  font-weight: bold;
}

.status-failed {
  color: #dc3545;
  font-weight: bold;
}

.status-pending {
  color: #ffc107;
  font-weight: bold;
}

.status-sending {
  color: #17a2b8;
  font-weight: bold;
}

.status-retry {
  color: #6f42c1;
  font-weight: bold;
}

.email-type {
  color: #007bff;
  font-weight: 500;
}

.error-message {
  color: #dc3545;
  font-size: 11px;
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 分页 */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.btn-page {
  background-color: #007bff;
  color: white;
}

.btn-page:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.page-info {
  font-size: 14px;
  color: #666;
}

/* 详情弹窗 */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.dialog {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 800px;
  max-height: 80vh;
  overflow-y: auto;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #eee;
}

.dialog-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.dialog-close {
  font-size: 24px;
  color: #999;
  cursor: pointer;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dialog-body {
  padding: 20px;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 15px;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  padding: 10px;
  border: 1px solid #eee;
  border-radius: 4px;
  background-color: #f9f9f9;
}

.detail-item.full-width {
  grid-column: 1 / -1;
  flex-direction: column;
  align-items: stretch;
}

.detail-label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
  min-width: 80px;
}

.detail-value {
  font-size: 14px;
  color: #333;
  word-break: break-all;
}

.detail-value.error {
  color: #dc3545;
}

.email-content {
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 10px;
  font-size: 12px;
  color: #333;
  max-height: 200px;
  overflow-y: auto;
  white-space: pre-wrap;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 20px;
  border-top: 1px solid #eee;
}

/* 加载提示 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.loading-content {
  background: white;
  padding: 20px 40px;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.loading-text {
  font-size: 16px;
  color: #333;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .email-log-viewer {
    padding: 10px;
  }

  .form-row {
    flex-direction: column;
    align-items: stretch;
  }

  .form-item {
    flex-direction: column;
    align-items: stretch;
  }

  .input, .picker-text {
    width: 100%;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }

  .detail-grid {
    grid-template-columns: 1fr;
  }

  .dialog {
    width: 95%;
    margin: 10px;
  }
}
</style>
