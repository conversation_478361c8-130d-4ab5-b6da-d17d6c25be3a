<template>
  <view class="email-permission-manager">
    <!-- 搜索区域 -->
    <view class="search-section">
      <view class="search-form">
        <view class="form-row">
          <view class="form-item">
            <text class="label">用户编号:</text>
            <input 
              v-model="searchForm.userNo" 
              placeholder="请输入用户编号" 
              class="input"
            />
          </view>
          <view class="form-item">
            <text class="label">部门名称:</text>
            <input 
              v-model="searchForm.deptName" 
              placeholder="请输入部门名称" 
              class="input"
            />
          </view>
          <view class="form-actions">
            <button @click="searchPermissions" class="btn btn-primary">查询</button>
            <button @click="resetSearch" class="btn btn-secondary">重置</button>
            <button @click="showAddDialog" class="btn btn-success">新增权限</button>
          </view>
        </view>
      </view>
    </view>

    <!-- 权限列表 -->
    <view class="permission-list">
      <view class="list-header">
        <text class="header-title">权限配置列表</text>
        <text class="total-count">共 {{ total }} 条记录</text>
      </view>
      
      <!-- 使用 uni-table 组件 -->
      <uni-table
        ref="table"
        :loading="loading"
        border
        stripe
        emptyText="暂无更多数据"
      >
        <!-- 表头 -->
        <uni-tr>
          <uni-th width="120" align="center">用户编号</uni-th>
          <uni-th width="150" align="center">部门名称</uni-th>
          <uni-th width="200" align="center">邮箱地址</uni-th>
          <uni-th width="100" align="center">报价V1</uni-th>
          <uni-th width="100" align="center">报价V2</uni-th>
          <uni-th width="100" align="center">报价V3</uni-th>
          <uni-th width="100" align="center">报价V4</uni-th>
          <uni-th width="100" align="center">报价V5</uni-th>
          <uni-th width="100" align="center">预估Z版</uni-th>
          <uni-th width="100" align="center">预估ZZ版</uni-th>
          <uni-th width="100" align="center">P版</uni-th>
          <uni-th width="100" align="center">更新通知</uni-th>
          <uni-th width="120" align="center">操作</uni-th>
        </uni-tr>

        <!-- 表格数据行 -->
        <uni-tr v-for="item in permissionList" :key="item.userNo">
          <uni-td align="center">{{ item.userNo }}</uni-td>
          <uni-td align="center">{{ item.deptName }}</uni-td>
          <uni-td align="center">{{ item.email }}</uni-td>
          <uni-td align="center">
            <text :class="getPermissionClass(item.pushQuoteV1)">
              {{ getPermissionText(item.pushQuoteV1) }}
            </text>
          </uni-td>
          <uni-td align="center">
            <text :class="getPermissionClass(item.pushQuoteV2)">
              {{ getPermissionText(item.pushQuoteV2) }}
            </text>
          </uni-td>
          <uni-td align="center">
            <text :class="getPermissionClass(item.pushQuoteV3)">
              {{ getPermissionText(item.pushQuoteV3) }}
            </text>
          </uni-td>
          <uni-td align="center">
            <text :class="getPermissionClass(item.pushQuoteV4)">
              {{ getPermissionText(item.pushQuoteV4) }}
            </text>
          </uni-td>
          <uni-td align="center">
            <text :class="getPermissionClass(item.pushQuoteV5)">
              {{ getPermissionText(item.pushQuoteV5) }}
            </text>
          </uni-td>
          <uni-td align="center">
            <text :class="getPermissionClass(item.pushEstimateZ)">
              {{ getPermissionText(item.pushEstimateZ) }}
            </text>
          </uni-td>
          <uni-td align="center">
            <text :class="getPermissionClass(item.pushEstimateZz)">
              {{ getPermissionText(item.pushEstimateZz) }}
            </text>
          </uni-td>
          <uni-td align="center">
            <text :class="getPermissionClass(item.pushPVersion)">
              {{ getPermissionText(item.pushPVersion) }}
            </text>
          </uni-td>
          <uni-td align="center">
            <text :class="getPermissionClass(item.updateEmailNotify)">
              {{ getPermissionText(item.updateEmailNotify) }}
            </text>
          </uni-td>
          <uni-td align="center">
            <button @click="editPermission(item)" class="btn btn-edit">编辑</button>
          </uni-td>
        </uni-tr>
      </uni-table>
      
      <!-- 分页 -->
      <view class="pagination">
        <button 
          @click="prevPage" 
          :disabled="currentPage <= 1"
          class="btn btn-page"
        >
          上一页
        </button>
        <text class="page-info">
          第 {{ currentPage }} 页 / 共 {{ totalPages }} 页
        </text>
        <button 
          @click="nextPage" 
          :disabled="currentPage >= totalPages"
          class="btn btn-page"
        >
          下一页
        </button>
      </view>
    </view>

    <!-- 新增/编辑权限弹窗 -->
    <view v-if="showDialog" class="dialog-overlay" @click="closeDialog">
      <view class="dialog" @click.stop>
        <view class="dialog-header">
          <text class="dialog-title">{{ isEdit ? '编辑权限' : '新增权限' }}</text>
          <text class="dialog-close" @click="closeDialog">×</text>
        </view>
        
        <view class="dialog-body">
          <view class="form-group">
            <text class="form-label">用户编号 *</text>
            <input 
              v-model="formData.userNo" 
              :disabled="isEdit"
              placeholder="请输入用户编号" 
              class="form-input"
            />
          </view>
          
          <view class="form-group">
            <text class="form-label">部门名称 *</text>
            <input 
              v-model="formData.deptName" 
              placeholder="请输入部门名称" 
              class="form-input"
            />
          </view>
          
          <view class="form-group">
            <text class="form-label">邮箱地址 *</text>
            <input 
              v-model="formData.email" 
              placeholder="请输入邮箱地址" 
              class="form-input"
            />
          </view>
          
          <!-- 权限配置 -->
          <view class="permission-config">
            <text class="section-title">权限配置</text>
            
            <view class="permission-grid">
              <view class="permission-item">
                <text class="permission-label">报价V1版:</text>
                <picker 
                  :value="getPermissionIndex(formData.pushQuoteV1)" 
                  :range="permissionOptions" 
                  @change="onPermissionChange('pushQuoteV1', $event)"
                >
                  <view class="picker-text">
                    {{ getPermissionText(formData.pushQuoteV1) }}
                  </view>
                </picker>
              </view>
              
              <view class="permission-item">
                <text class="permission-label">报价V2版:</text>
                <picker 
                  :value="getPermissionIndex(formData.pushQuoteV2)" 
                  :range="permissionOptions" 
                  @change="onPermissionChange('pushQuoteV2', $event)"
                >
                  <view class="picker-text">
                    {{ getPermissionText(formData.pushQuoteV2) }}
                  </view>
                </picker>
              </view>
              
              <view class="permission-item">
                <text class="permission-label">报价V3版:</text>
                <picker 
                  :value="getPermissionIndex(formData.pushQuoteV3)" 
                  :range="permissionOptions" 
                  @change="onPermissionChange('pushQuoteV3', $event)"
                >
                  <view class="picker-text">
                    {{ getPermissionText(formData.pushQuoteV3) }}
                  </view>
                </picker>
              </view>
              
              <view class="permission-item">
                <text class="permission-label">报价V4版:</text>
                <picker 
                  :value="getPermissionIndex(formData.pushQuoteV4)" 
                  :range="permissionOptions" 
                  @change="onPermissionChange('pushQuoteV4', $event)"
                >
                  <view class="picker-text">
                    {{ getPermissionText(formData.pushQuoteV4) }}
                  </view>
                </picker>
              </view>
              
              <view class="permission-item">
                <text class="permission-label">报价V5版:</text>
                <picker 
                  :value="getPermissionIndex(formData.pushQuoteV5)" 
                  :range="permissionOptions" 
                  @change="onPermissionChange('pushQuoteV5', $event)"
                >
                  <view class="picker-text">
                    {{ getPermissionText(formData.pushQuoteV5) }}
                  </view>
                </picker>
              </view>
              
              <view class="permission-item">
                <text class="permission-label">预估Z版:</text>
                <picker 
                  :value="getPermissionIndex(formData.pushEstimateZ)" 
                  :range="permissionOptions" 
                  @change="onPermissionChange('pushEstimateZ', $event)"
                >
                  <view class="picker-text">
                    {{ getPermissionText(formData.pushEstimateZ) }}
                  </view>
                </picker>
              </view>
              
              <view class="permission-item">
                <text class="permission-label">预估ZZ版:</text>
                <picker 
                  :value="getPermissionIndex(formData.pushEstimateZz)" 
                  :range="permissionOptions" 
                  @change="onPermissionChange('pushEstimateZz', $event)"
                >
                  <view class="picker-text">
                    {{ getPermissionText(formData.pushEstimateZz) }}
                  </view>
                </picker>
              </view>
              
              <view class="permission-item">
                <text class="permission-label">P版:</text>
                <picker 
                  :value="getPermissionIndex(formData.pushPVersion)" 
                  :range="permissionOptions" 
                  @change="onPermissionChange('pushPVersion', $event)"
                >
                  <view class="picker-text">
                    {{ getPermissionText(formData.pushPVersion) }}
                  </view>
                </picker>
              </view>
              
              <view class="permission-item">
                <text class="permission-label">更新通知:</text>
                <picker 
                  :value="getPermissionIndex(formData.updateEmailNotify)" 
                  :range="permissionOptions" 
                  @change="onPermissionChange('updateEmailNotify', $event)"
                >
                  <view class="picker-text">
                    {{ getPermissionText(formData.updateEmailNotify) }}
                  </view>
                </picker>
              </view>
            </view>
          </view>
        </view>
        
        <view class="dialog-footer">
          <button @click="closeDialog" class="btn btn-cancel">取消</button>
          <button @click="savePermission" class="btn btn-confirm">确定</button>
        </view>
      </view>
    </view>

    <!-- 加载提示 -->
    <view v-if="loading" class="loading-overlay">
      <view class="loading-content">
        <text class="loading-text">加载中...</text>
      </view>
    </view>
  </view>
</template>

<script>
import {
  getPermissionList,
  addPermission,
  updatePermission,
  PERMISSION_STATUS_NAMES
} from '@/api/email.js';

export default {
  name: 'EmailPermissionManager',
  data() {
    return {
      // 搜索表单
      searchForm: {
        userNo: '',
        deptName: ''
      },
      
      // 权限列表
      permissionList: [],
      total: 0,
      currentPage: 1,
      pageSize: 10,
      totalPages: 0,
      
      // 弹窗相关
      showDialog: false,
      isEdit: false,
      formData: {
        userNo: '',
        deptName: '',
        email: '',
        pushQuoteV1: 'N',
        pushQuoteV2: 'N',
        pushQuoteV3: 'N',
        pushQuoteV4: 'N',
        pushQuoteV5: 'N',
        pushEstimateZ: 'N',
        pushEstimateZz: 'N',
        pushPVersion: 'N',
        updateEmailNotify: 'N'
      },
      
      // 权限选项
      permissionOptions: ['有权限', '无权限', '待定'],
      permissionValues: ['Y', 'N', 'P'],
      
      // 加载状态
      loading: false
    };
  },
  
  mounted() {
    this.loadPermissions();
  },
  
  methods: {
    /**
     * 加载权限列表
     */
    async loadPermissions() {
      try {
        this.loading = true;
        const params = {
          pageNo: this.currentPage,
          pageSize: this.pageSize,
          ...this.searchForm
        };
        
        const response = await getPermissionList(params);
        if (response.code === 1) {
          this.permissionList = response.data.list || [];
          this.total = response.data.total || 0;
          this.totalPages = Math.ceil(this.total / this.pageSize);
        } else {
          uni.showToast({
            title: response.message || '加载失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('加载权限列表失败:', error);
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },
    
    /**
     * 搜索权限
     */
    searchPermissions() {
      this.currentPage = 1;
      this.loadPermissions();
    },
    
    /**
     * 重置搜索
     */
    resetSearch() {
      this.searchForm = {
        userNo: '',
        deptName: ''
      };
      this.currentPage = 1;
      this.loadPermissions();
    },
    
    /**
     * 上一页
     */
    prevPage() {
      if (this.currentPage > 1) {
        this.currentPage--;
        this.loadPermissions();
      }
    },
    
    /**
     * 下一页
     */
    nextPage() {
      if (this.currentPage < this.totalPages) {
        this.currentPage++;
        this.loadPermissions();
      }
    },
    
    /**
     * 显示新增弹窗
     */
    showAddDialog() {
      this.isEdit = false;
      this.formData = {
        userNo: '',
        deptName: '',
        email: '',
        pushQuoteV1: 'N',
        pushQuoteV2: 'N',
        pushQuoteV3: 'N',
        pushQuoteV4: 'N',
        pushQuoteV5: 'N',
        pushEstimateZ: 'N',
        pushEstimateZz: 'N',
        pushPVersion: 'N',
        updateEmailNotify: 'N'
      };
      this.showDialog = true;
    },
    
    /**
     * 编辑权限
     */
    editPermission(item) {
      this.isEdit = true;
      this.formData = { ...item };
      this.showDialog = true;
    },
    
    /**
     * 关闭弹窗
     */
    closeDialog() {
      this.showDialog = false;
    },
    
    /**
     * 保存权限
     */
    async savePermission() {
      // 表单验证
      if (!this.formData.userNo || !this.formData.deptName || !this.formData.email) {
        uni.showToast({
          title: '请填写必填项',
          icon: 'none'
        });
        return;
      }
      
      // 邮箱格式验证
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(this.formData.email)) {
        uni.showToast({
          title: '请输入正确的邮箱格式',
          icon: 'none'
        });
        return;
      }
      
      try {
        this.loading = true;
        const data = {
          ...this.formData,
          createUser: 'ADMIN', // 这里应该从用户信息中获取
          updateUser: 'ADMIN'  // 这里应该从用户信息中获取
        };
        
        let response;
        if (this.isEdit) {
          response = await updatePermission(data);
        } else {
          response = await addPermission(data);
        }
        
        if (response.code === 1) {
          uni.showToast({
            title: this.isEdit ? '更新成功' : '新增成功',
            icon: 'success'
          });
          this.closeDialog();
          this.loadPermissions();
        } else {
          uni.showToast({
            title: response.message || '操作失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('保存权限失败:', error);
        uni.showToast({
          title: '操作失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },
    
    /**
     * 权限选择器变化
     */
    onPermissionChange(field, event) {
      const index = event.detail.value;
      this.formData[field] = this.permissionValues[index];
    },
    
    /**
     * 获取权限索引
     */
    getPermissionIndex(value) {
      return this.permissionValues.indexOf(value);
    },
    
    /**
     * 获取权限文本
     */
    getPermissionText(value) {
      return PERMISSION_STATUS_NAMES[value] || '无权限';
    },
    
    /**
     * 获取权限样式类
     */
    getPermissionClass(value) {
      return {
        'permission-yes': value === 'Y',
        'permission-no': value === 'N',
        'permission-pending': value === 'P'
      };
    }
  }
};
</script>

<style scoped>
.email-permission-manager {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 搜索区域 */
.search-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.search-form .form-row {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.form-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.label {
  font-size: 14px;
  color: #333;
  white-space: nowrap;
}

.input {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  width: 200px;
}

.form-actions {
  display: flex;
  gap: 10px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s;
}

.btn-primary {
  background-color: #007bff;
  color: white;
}

.btn-primary:hover {
  background-color: #0056b3;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-success {
  background-color: #28a745;
  color: white;
}

.btn-edit {
  background-color: #ffc107;
  color: #212529;
  padding: 4px 8px;
  font-size: 12px;
}

/* 权限列表 */
.permission-list {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.header-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.total-count {
  font-size: 14px;
  color: #666;
}



/* 权限状态样式 */
.permission-yes {
  color: #28a745;
  font-weight: bold;
}

.permission-no {
  color: #dc3545;
}

.permission-pending {
  color: #ffc107;
}

/* 分页 */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.btn-page {
  background-color: #007bff;
  color: white;
}

.btn-page:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.page-info {
  font-size: 14px;
  color: #666;
}

/* 弹窗 */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}

.dialog {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 800px;
  max-height: 80vh;
  overflow-y: auto;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #eee;
}

.dialog-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.dialog-close {
  font-size: 24px;
  color: #999;
  cursor: pointer;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dialog-body {
  padding: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  color: #333;
  font-weight: bold;
}

.form-input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.form-input:disabled {
  background-color: #f5f5f5;
  color: #999;
}

/* 权限配置 */
.permission-config {
  margin-top: 30px;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.permission-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
}

.permission-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px;
  border: 1px solid #eee;
  border-radius: 4px;
  background-color: #f9f9f9;
}

.permission-label {
  font-size: 14px;
  color: #333;
}

.picker-text {
  padding: 6px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
  font-size: 14px;
  min-width: 80px;
  text-align: center;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 20px;
  border-top: 1px solid #eee;
}

.btn-cancel {
  background-color: #6c757d;
  color: white;
}

.btn-confirm {
  background-color: #007bff;
  color: white;
}

/* 加载提示 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.loading-content {
  background: white;
  padding: 20px 40px;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.loading-text {
  font-size: 16px;
  color: #333;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-form .form-row {
    flex-direction: column;
    align-items: stretch;
  }

  .form-item {
    flex-direction: column;
    align-items: stretch;
  }

  .input {
    width: 100%;
  }

  .permission-grid {
    grid-template-columns: 1fr;
  }

  .dialog {
    width: 95%;
    margin: 10px;
  }
}
</style>
