<template>
  <view class="email-page">
    <!-- 页面头部 -->
    <view class="page-header">
      <view class="header-content">
        <text class="page-title">邮件推送管理</text>
        <text class="page-subtitle">Email Push Management System</text>
      </view>
      <view class="header-actions">
        <button @click="refreshAll" class="btn btn-refresh">
          <text class="btn-icon">🔄</text>
          <text>刷新</text>
        </button>
      </view>
    </view>

    <!-- 功能导航 -->
    <view class="nav-tabs">
      <view
        v-for="tab in tabs"
        :key="tab.key"
        :class="['nav-tab', { active: activeTab === tab.key }]"
        @click="switchTab(tab.key)"
      >
        <text class="tab-icon">{{ tab.icon }}</text>
        <text class="tab-text">{{ tab.name }}</text>
        <view v-if="tab.badge" class="tab-badge">{{ tab.badge }}</view>
      </view>
    </view>

    <!-- 内容区域 -->
    <view class="content-area">
      <!-- 权限管理 -->
      <view v-if="activeTab === 'permission'" class="tab-content">
        <EmailPermissionManager ref="permissionManager" />
      </view>

      <!-- 日志查询 -->
      <view v-if="activeTab === 'logs'" class="tab-content">
        <EmailLogViewer ref="logViewer" />
      </view>

      <!-- 配置管理 -->
      <view v-if="activeTab === 'config'" class="tab-content">
        <EmailConfigManager ref="configManager" />
      </view>

      <!-- 模板管理 -->
      <view v-if="activeTab === 'template'" class="tab-content">
        <EmailTemplateManager ref="templateManager" />
      </view>

      <!-- 系统概览 -->
      <view v-if="activeTab === 'overview'" class="tab-content">
        <view class="overview-section">
          <!-- 快速统计 -->
          <view class="quick-stats">
            <view class="section-header">
              <text class="section-title">系统概览</text>
              <button @click="refreshOverview" class="btn btn-refresh-small">刷新</button>
            </view>

            <view class="stats-grid">
              <view class="stat-card primary">
                <view class="stat-icon">📧</view>
                <view class="stat-info">
                  <text class="stat-number">{{ overview.totalEmails || 0 }}</text>
                  <text class="stat-label">总邮件数</text>
                </view>
              </view>

              <view class="stat-card success">
                <view class="stat-icon">✅</view>
                <view class="stat-info">
                  <text class="stat-number">{{ overview.successEmails || 0 }}</text>
                  <text class="stat-label">发送成功</text>
                </view>
              </view>

              <view class="stat-card error">
                <view class="stat-icon">❌</view>
                <view class="stat-info">
                  <text class="stat-number">{{ overview.failedEmails || 0 }}</text>
                  <text class="stat-label">发送失败</text>
                </view>
              </view>

              <view class="stat-card warning">
                <view class="stat-icon">⏳</view>
                <view class="stat-info">
                  <text class="stat-number">{{ overview.pendingEmails || 0 }}</text>
                  <text class="stat-label">待发送</text>
                </view>
              </view>

              <view class="stat-card info">
                <view class="stat-icon">👥</view>
                <view class="stat-info">
                  <text class="stat-number">{{ overview.totalUsers || 0 }}</text>
                  <text class="stat-label">用户数量</text>
                </view>
              </view>

              <view class="stat-card">
                <view class="stat-icon">📊</view>
                <view class="stat-info">
                  <text class="stat-number">{{ successRate }}%</text>
                  <text class="stat-label">成功率</text>
                </view>
              </view>
            </view>
          </view>

          <!-- 快速操作 -->
          <view class="quick-actions">
            <view class="section-header">
              <text class="section-title">快速操作</text>
            </view>

            <view class="actions-grid">
              <view class="action-card" @click="viewLogs">
                <view class="action-icon">📋</view>
                <text class="action-title">查看日志</text>
                <text class="action-desc">邮件发送记录</text>
              </view>

              <view class="action-card" @click="managePermissions">
                <view class="action-icon">🔐</view>
                <text class="action-title">权限管理</text>
                <text class="action-desc">用户权限配置</text>
              </view>

              <view class="action-card" @click="manageTemplates">
                <view class="action-icon">📄</view>
                <text class="action-title">模板管理</text>
                <text class="action-desc">邮件模板配置</text>
              </view>

              <view class="action-card" @click="configSystem">
                <view class="action-icon">⚙️</view>
                <text class="action-title">系统配置</text>
                <text class="action-desc">SMTP设置</text>
              </view>
            </view>
          </view>

          <!-- 最近活动 -->
          <view class="recent-activities">
            <view class="section-header">
              <text class="section-title">最近活动</text>
            </view>

            <view class="activities-list">
              <view
                v-for="(activity, index) in recentActivities"
                :key="index"
                class="activity-item"
              >
                <view class="activity-icon" :class="activity.type">
                  {{ getActivityIcon(activity.type) }}
                </view>
                <view class="activity-content">
                  <text class="activity-title">{{ activity.title }}</text>
                  <text class="activity-desc">{{ activity.description }}</text>
                  <text class="activity-time">{{ activity.time }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 全局加载提示 -->
    <view v-if="globalLoading" class="global-loading">
      <view class="loading-content">
        <view class="loading-spinner"></view>
        <text class="loading-text">{{ loadingText }}</text>
      </view>
    </view>
  </view>
</template>

<script>
import EmailPermissionManager from '@/components/EmailPermissionManager.vue';
import EmailLogViewer from '@/components/EmailLogViewer.vue';
import EmailConfigManager from '@/components/EmailConfigManager.vue';
import EmailTemplateManager from '@/components/EmailTemplateManager.vue';
import { getEmailStatistics } from '@/api/email.js';

export default {
  name: 'EmailPage',
  components: {
    EmailPermissionManager,
    EmailLogViewer,
    EmailConfigManager,
    EmailTemplateManager
  },

  data() {
    return {
      // 当前激活的标签页
      activeTab: 'overview',

      // 标签页配置
      tabs: [
        { key: 'overview', name: '系统概览', icon: '📊' },
        { key: 'permission', name: '权限管理', icon: '🔐' },
        { key: 'logs', name: '日志查询', icon: '📋' },
        { key: 'template', name: '模板管理', icon: '📄' },
        { key: 'config', name: '系统配置', icon: '⚙️' }
      ],

      // 系统概览数据
      overview: {
        totalEmails: 0,
        successEmails: 0,
        failedEmails: 0,
        pendingEmails: 0,
        totalUsers: 0
      },

      // 最近活动
      recentActivities: [],

      // 全局加载状态
      globalLoading: false,
      loadingText: '加载中...'
    };
  },

  computed: {
    /**
     * 成功率
     */
    successRate() {
      if (this.overview.totalEmails === 0) return 0;
      return Math.round((this.overview.successEmails / this.overview.totalEmails) * 100);
    }
  },

  mounted() {
    this.initPage();
  },

  methods: {
    /**
     * 初始化页面
     */
    async initPage() {
      try {
        this.globalLoading = true;
        this.loadingText = '初始化系统...';

        await this.loadOverviewData();
        this.initRecentActivities();

        // 检查URL参数，如果有指定标签页则切换
        try {
          // @ts-ignore
          const pages = getCurrentPages();
          const currentPage = pages[pages.length - 1];
          if (currentPage.options && currentPage.options.tab) {
            this.activeTab = currentPage.options.tab;
          }
        } catch (error) {
          console.log('获取页面参数失败:', error);
        }
      } catch (error) {
        console.error('初始化页面失败:', error);
        uni.showToast({
          title: '初始化失败',
          icon: 'none'
        });
      } finally {
        this.globalLoading = false;
      }
    },

    /**
     * 切换标签页
     */
    switchTab(tabKey) {
      this.activeTab = tabKey;

      // 更新页面标题
      const tab = this.tabs.find(t => t.key === tabKey);
      if (tab) {
        uni.setNavigationBarTitle({
          title: `邮件系统 - ${tab.name}`
        });
      }
    },

    /**
     * 加载概览数据
     */
    async loadOverviewData() {
      try {
        // 获取最近7天的统计数据
        const endDate = new Date();
        const startDate = new Date(endDate.getTime() - 7 * 24 * 60 * 60 * 1000);

        const response = await getEmailStatistics({
          startDate: startDate.toISOString().split('T')[0],
          endDate: endDate.toISOString().split('T')[0]
        });

        if (response.code === 200 && response.data) {
          this.overview = {
            totalEmails: response.data.total || 0,
            successEmails: response.data.success || 0,
            failedEmails: response.data.failed || 0,
            pendingEmails: response.data.pending || 0,
            totalUsers: response.data.users || 0
          };
        }
      } catch (error) {
        console.error('加载概览数据失败:', error);
      }
    },

    /**
     * 刷新概览数据
     */
    async refreshOverview() {
      this.globalLoading = true;
      this.loadingText = '刷新数据中...';

      try {
        await this.loadOverviewData();
        uni.showToast({
          title: '刷新成功',
          icon: 'success'
        });
      } catch (error) {
        uni.showToast({
          title: '刷新失败',
          icon: 'none'
        });
      } finally {
        this.globalLoading = false;
      }
    },

    /**
     * 刷新所有数据
     */
    async refreshAll() {
      this.globalLoading = true;
      this.loadingText = '刷新所有数据...';

      try {
        await this.loadOverviewData();

        // 刷新当前激活组件的数据
        switch (this.activeTab) {
          case 'permission':
            if (this.$refs.permissionManager) {
              this.$refs.permissionManager.loadPermissions();
            }
            break;
          case 'logs':
            if (this.$refs.logViewer) {
              this.$refs.logViewer.loadLogs();
              this.$refs.logViewer.loadStatistics();
            }
            break;
          case 'template':
            if (this.$refs.templateManager) {
              this.$refs.templateManager.loadTemplates();
            }
            break;
          case 'config':
            if (this.$refs.configManager) {
              this.$refs.configManager.loadConfig();
            }
            break;
        }

        uni.showToast({
          title: '刷新成功',
          icon: 'success'
        });
      } catch (error) {
        uni.showToast({
          title: '刷新失败',
          icon: 'none'
        });
      } finally {
        this.globalLoading = false;
      }
    },

    /**
     * 初始化最近活动
     */
    initRecentActivities() {
      // 这里可以从本地存储或服务器加载最近活动
      this.recentActivities = [
        {
          type: 'success',
          title: '邮件发送成功',
          description: '报价邮件V1版发送至客户',
          time: '2分钟前'
        },
        {
          type: 'config',
          title: '配置更新',
          description: 'SMTP配置已更新',
          time: '10分钟前'
        },
        {
          type: 'permission',
          title: '权限变更',
          description: '用户权限配置已修改',
          time: '1小时前'
        },
        {
          type: 'error',
          title: '发送失败',
          description: '预估邮件发送失败',
          time: '2小时前'
        }
      ];
    },



    /**
     * 快速操作 - 查看日志
     */
    viewLogs() {
      this.switchTab('logs');
    },

    /**
     * 快速操作 - 权限管理
     */
    managePermissions() {
      this.switchTab('permission');
    },

    /**
     * 快速操作 - 模板管理
     */
    manageTemplates() {
      this.switchTab('template');
    },

    /**
     * 快速操作 - 系统配置
     */
    configSystem() {
      this.switchTab('config');
    },

    /**
     * 获取活动图标
     */
    getActivityIcon(type) {
      const icons = {
        success: '✅',
        error: '❌',
        config: '⚙️',
        permission: '🔐',
        send: '📧'
      };
      return icons[type] || '📝';
    },


  }
};
</script>

<style scoped>
.email-page {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.header-content {
  flex: 1;
}

.page-title {
  font-size: 24px;
  font-weight: bold;
  display: block;
  margin-bottom: 5px;
}

.page-subtitle {
  font-size: 14px;
  opacity: 0.9;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  gap: 5px;
}

.btn-refresh {
  background-color: rgba(255,255,255,0.2);
  color: white;
  border: 1px solid rgba(255,255,255,0.3);
}

.btn-refresh:hover {
  background-color: rgba(255,255,255,0.3);
}

.btn-refresh-small {
  background-color: #17a2b8;
  color: white;
  padding: 6px 12px;
  font-size: 12px;
}

.btn-icon {
  font-size: 16px;
}

/* 导航标签 */
.nav-tabs {
  background: white;
  display: flex;
  border-bottom: 1px solid #eee;
  overflow-x: auto;
  padding: 0 20px;
}

.nav-tab {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 15px 20px;
  cursor: pointer;
  border-bottom: 3px solid transparent;
  transition: all 0.3s;
  white-space: nowrap;
  position: relative;
}

.nav-tab:hover {
  background-color: #f8f9fa;
}

.nav-tab.active {
  border-bottom-color: #007bff;
  background-color: #f8f9fa;
}

.tab-icon {
  font-size: 18px;
}

.tab-text {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.nav-tab.active .tab-text {
  color: #007bff;
}

.tab-badge {
  background-color: #dc3545;
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 16px;
  text-align: center;
}

/* 内容区域 */
.content-area {
  padding: 20px;
}

.tab-content {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 概览页面 */
.overview-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.quick-stats,
.quick-actions,
.recent-activities {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #007bff;
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

/* 统计卡片 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.stat-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  padding: 20px;
  color: white;
  display: flex;
  align-items: center;
  gap: 15px;
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  transition: transform 0.3s;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-card.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-card.success {
  background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
}

.stat-card.error {
  background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
}

.stat-card.warning {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-card.info {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon {
  font-size: 32px;
  opacity: 0.9;
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  display: block;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.9;
}

/* 快速操作 */
.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.action-card {
  border: 2px solid #eee;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
  background: white;
}

.action-card:hover {
  border-color: #007bff;
  box-shadow: 0 4px 8px rgba(0,123,255,0.1);
  transform: translateY(-2px);
}

.action-icon {
  font-size: 32px;
  margin-bottom: 10px;
}

.action-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 5px;
}

.action-desc {
  font-size: 12px;
  color: #666;
}

/* 最近活动 */
.activities-list {
  max-height: 400px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  padding: 15px 0;
  border-bottom: 1px solid #eee;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: white;
  flex-shrink: 0;
}

.activity-icon.success {
  background-color: #28a745;
}

.activity-icon.error {
  background-color: #dc3545;
}

.activity-icon.config {
  background-color: #6f42c1;
}

.activity-icon.permission {
  background-color: #fd7e14;
}

.activity-icon.send {
  background-color: #007bff;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-size: 14px;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 4px;
}

.activity-desc {
  font-size: 13px;
  color: #666;
  display: block;
  margin-bottom: 4px;
}

.activity-time {
  font-size: 12px;
  color: #999;
}

/* 全局加载 */
.global-loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 3000;
}

.loading-content {
  background: white;
  padding: 30px 40px;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0,0,0,0.2);
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 16px;
  color: #333;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
    text-align: center;
  }

  .nav-tabs {
    padding: 0 10px;
  }

  .nav-tab {
    padding: 12px 15px;
  }

  .content-area {
    padding: 10px;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }

  .actions-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }

  .stat-card {
    padding: 15px;
  }

  .stat-number {
    font-size: 24px;
  }

  .stat-icon {
    font-size: 24px;
  }

  .action-card {
    padding: 15px;
  }

  .action-icon {
    font-size: 24px;
  }
}
</style>