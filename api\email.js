import request from '@/utils/request';

/**
 * 邮件推送API接口
 * 基于后端邮件推送系统API文档实现
 */

// ==================== 邮件权限管理 API ====================

/**
 * 查询权限列表
 * @param {Object} params - 查询参数
 * @param {number} params.pageNo - 页码
 * @param {number} params.pageSize - 每页大小
 * @param {string} params.userNo - 用户编号
 * @param {string} params.deptName - 部门名称
 * @returns {Promise} 权限列表数据
 */
export function getPermissionList(params) {
  return request({
    url: '/emailPushPermission/query',
    method: 'get',
    params
  });
}

/**
 * 新增权限配置
 * @param {Object} data - 权限配置数据
 * @param {string} data.userNo - 用户编号
 * @param {string} data.deptName - 部门名称
 * @param {string} data.email - 邮箱地址
 * @param {string} data.pushEstimateZ - 推送预估Z版权限 (Y/N/P)
 * @param {string} data.pushEstimateZz - 推送预估ZZ版权限 (Y/N/P)
 * @param {string} data.updateEmailNotify - 更新邮件通知权限 (Y/N/P)
 * @param {string} data.createUser - 创建用户
 * @returns {Promise} 操作结果
 */
export function addPermission(data) {
  return request({
    url: '/emailPushPermission/add',
    method: 'post',
    data
  });
}

/**
 * 更新权限配置
 * @param {Object} data - 权限配置数据
 * @param {string} data.userNo - 用户编号
 * @param {string} data.pushQuoteV1 - 推送报价V1权限 (Y/N/P)
 * @param {string} data.pushQuoteV2 - 推送报价V2权限 (Y/N/P)
 * @param {string} data.pushQuoteV3 - 推送报价V3权限 (Y/N/P)
 * @param {string} data.pushQuoteV4 - 推送报价V4权限 (Y/N/P)
 * @param {string} data.pushQuoteV5 - 推送报价V5权限 (Y/N/P)
 * @param {string} data.pushEstimateZ - 推送预估Z版权限 (Y/N/P)
 * @param {string} data.pushEstimateZz - 推送预估ZZ版权限 (Y/N/P)
 * @param {string} data.pushPVersion - 推送P版权限 (Y/N/P)
 * @param {string} data.updateEmailNotify - 更新邮件通知权限 (Y/N/P)
 * @param {string} data.updateUser - 更新用户
 * @returns {Promise} 操作结果
 */
export function updatePermission(data) {
  return request({
    url: '/emailPushPermission/update',
    method: 'post',
    data
  });
}

/**
 * 检查用户权限
 * @param {string} userNo - 用户编号
 * @param {string} emailType - 邮件类型
 * @returns {Promise} 权限检查结果
 */
export function checkPermission(userNo, emailType) {
  return request({
    url: '/emailPushPermission/hasPermission',
    method: 'get',
    params: { userNo, emailType }
  });
}

// ==================== 邮件发送 API ====================

/**
 * 发送报价邮件
 * @param {string} quoteVersion - 报价版本 (V1/V2/V3/V4/V5)
 * @param {string} businessId - 业务ID
 * @param {string} sendUser - 发送用户
 * @returns {Promise} 发送结果
 */
export function sendQuoteEmail(quoteVersion, businessId, sendUser) {
  return request({
    url: '/emailPush/sendQuote',
    method: 'post',
    params: { quoteVersion, businessId, sendUser }
  });
}

/**
 * 发送预估邮件
 * @param {string} estimateVersion - 预估版本 (Z/ZZ)
 * @param {string} businessId - 业务ID
 * @param {string} sendUser - 发送用户
 * @returns {Promise} 发送结果
 */
export function sendEstimateEmail(estimateVersion, businessId, sendUser) {
  return request({
    url: '/emailPush/sendEstimate',
    method: 'post',
    params: { estimateVersion, businessId, sendUser }
  });
}

/**
 * 发送P版邮件
 * @param {string} businessId - 业务ID
 * @param {string} sendUser - 发送用户
 * @returns {Promise} 发送结果
 */
export function sendPVersionEmail(businessId, sendUser) {
  return request({
    url: '/emailPush/sendPVersion',
    method: 'post',
    params: { businessId, sendUser }
  });
}

/**
 * 发送更新通知邮件
 * @param {string} businessId - 业务ID
 * @param {string} sendUser - 发送用户
 * @returns {Promise} 发送结果
 */
export function sendUpdateNotifyEmail(businessId, sendUser) {
  return request({
    url: '/emailPush/sendUpdateNotify',
    method: 'post',
    params: { businessId, sendUser }
  });
}

/**
 * 自定义邮件发送
 * @param {Object} data - 邮件发送数据
 * @param {string} data.emailType - 邮件类型
 * @param {string} data.businessId - 业务ID
 * @param {Object} data.templateVariables - 模板变量
 * @param {string} data.sendUser - 发送用户
 * @returns {Promise} 发送结果
 */
export function sendCustomEmail(data) {
  return request({
    url: '/emailPush/send',
    method: 'post',
    data
  });
}

/**
 * 批量发送邮件
 * @param {Array} emailList - 邮件列表
 * @returns {Promise} 批量发送结果
 */
export function batchSendEmails(emailList) {
  return request({
    url: '/emailPush/batchSend',
    method: 'post',
    data: emailList
  });
}

// ==================== 邮件日志查询 API ====================

/**
 * 查询邮件发送日志
 * @param {Object} params - 查询参数
 * @param {number} params.pageNo - 页码
 * @param {number} params.pageSize - 每页大小
 * @param {string} params.emailType - 邮件类型
 * @param {string} params.sendStatus - 发送状态
 * @param {string} params.startDate - 开始日期
 * @param {string} params.endDate - 结束日期
 * @returns {Promise} 日志列表数据
 */
export function getEmailLogs(params) {
  return request({
    url: '/emailPush/queryLogs',
    method: 'get',
    params
  });
}

/**
 * 根据业务ID查询日志
 * @param {string} businessId - 业务ID
 * @returns {Promise} 日志数据
 */
export function getLogsByBusinessId(businessId) {
  return request({
    url: '/emailPush/getLogsByBusinessId',
    method: 'get',
    params: { businessId }
  });
}

/**
 * 获取发送统计
 * @param {Object} params - 统计参数
 * @param {string} params.emailType - 邮件类型
 * @param {string} params.startDate - 开始日期
 * @param {string} params.endDate - 结束日期
 * @returns {Promise} 统计数据
 */
export function getEmailStatistics(params) {
  return request({
    url: '/emailPush/getStatistics',
    method: 'get',
    params
  });
}

/**
 * 重试单个邮件
 * @param {number} logId - 日志ID
 * @returns {Promise} 重试结果
 */
export function retryEmail(logId) {
  return request({
    url: '/emailPush/retry',
    method: 'post',
    params: { logId }
  });
}

/**
 * 批量重试失败邮件
 * @returns {Promise} 批量重试结果
 */
export function batchRetryEmails() {
  return request({
    url: '/emailPush/batchRetry',
    method: 'post'
  });
}

// ==================== 邮件模板管理 API ====================

/**
 * 分页查询邮件模板列表
 * @param {Object} params - 查询参数
 * @param {number} params.pageNo - 页码
 * @param {number} params.pageSize - 每页大小
 * @param {string} params.emailType - 邮件类型
 * @param {string} params.language - 语言
 * @param {string} params.status - 状态
 * @param {string} params.templateName - 模板名称
 * @returns {Promise} 模板列表数据
 */
export function getTemplateList(params) {
  return request({
    url: '/emailTemplate/query',
    method: 'get',
    params
  });
}

/**
 * 根据ID查询邮件模板
 * @param {number} id - 模板ID
 * @returns {Promise} 模板信息
 */
export function getTemplateById(id) {
  return request({
    url: '/emailTemplate/getById',
    method: 'get',
    params: { id }
  });
}

/**
 * 根据模板代码查询邮件模板
 * @param {string} templateCode - 模板代码
 * @returns {Promise} 模板信息
 */
export function getTemplateByCode(templateCode) {
  return request({
    url: '/emailTemplate/getByCode',
    method: 'get',
    params: { templateCode }
  });
}

/**
 * 根据邮件类型获取默认模板
 * @param {string} emailType - 邮件类型
 * @param {string} language - 语言
 * @returns {Promise} 模板信息
 */
export function getDefaultTemplate(emailType, language = 'zh_CN') {
  return request({
    url: '/emailTemplate/getDefault',
    method: 'get',
    params: { emailType, language }
  });
}

/**
 * 根据邮件类型查询所有模板
 * @param {string} emailType - 邮件类型
 * @returns {Promise} 模板列表
 */
export function getTemplatesByEmailType(emailType) {
  return request({
    url: '/emailTemplate/getByEmailType',
    method: 'get',
    params: { emailType }
  });
}

/**
 * 查询所有启用的模板
 * @returns {Promise} 模板列表
 */
export function getAllEnabledTemplates() {
  return request({
    url: '/emailTemplate/getAllEnabled',
    method: 'get'
  });
}

/**
 * 新增邮件模板
 * @param {Object} template - 模板信息
 * @returns {Promise} 操作结果
 */
export function addTemplate(template) {
  return request({
    url: '/emailTemplate/add',
    method: 'post',
    data: template
  });
}

/**
 * 更新邮件模板
 * @param {Object} template - 模板信息
 * @returns {Promise} 操作结果
 */
export function updateTemplate(template) {
  return request({
    url: '/emailTemplate/update',
    method: 'post',
    data: template
  });
}

/**
 * 删除邮件模板
 * @param {number} id - 模板ID
 * @returns {Promise} 操作结果
 */
export function deleteTemplate(id) {
  return request({
    url: '/emailTemplate/delete',
    method: 'post',
    params: { id }
  });
}

/**
 * 设置默认模板
 * @param {number} id - 模板ID
 * @returns {Promise} 操作结果
 */
export function setDefaultTemplate(id) {
  return request({
    url: '/emailTemplate/setDefault',
    method: 'post',
    params: { id }
  });
}

/**
 * 渲染邮件模板
 * @param {string} templateCode - 模板代码
 * @param {Object} variables - 变量Map
 * @returns {Promise} 渲染结果
 */
export function renderTemplate(templateCode, variables) {
  return request({
    url: '/emailTemplate/render',
    method: 'post',
    params: { templateCode },
    data: variables
  });
}

/**
 * 根据邮件类型渲染模板
 * @param {string} emailType - 邮件类型
 * @param {string} language - 语言
 * @param {Object} variables - 变量Map
 * @returns {Promise} 渲染结果
 */
export function renderTemplateByType(emailType, language = 'zh_CN', variables) {
  return request({
    url: '/emailTemplate/renderByType',
    method: 'post',
    params: { emailType, language },
    data: variables
  });
}

/**
 * 验证模板语法
 * @param {Object} request - 验证请求
 * @param {string} request.subjectTemplate - 主题模板
 * @param {string} request.contentTemplate - 内容模板
 * @param {Object} request.variables - 变量Map
 * @returns {Promise} 验证结果
 */
export function validateTemplate(validateRequest) {
  return request({
    url: '/emailTemplate/validate',
    method: 'post',
    data: validateRequest
  });
}

/**
 * 复制模板
 * @param {Object} request - 复制请求
 * @param {number} request.sourceId - 源模板ID
 * @param {string} request.newCode - 新模板代码
 * @param {string} request.newName - 新模板名称
 * @param {string} request.createUser - 创建用户
 * @returns {Promise} 操作结果
 */
export function copyTemplate(copyRequest) {
  return request({
    url: '/emailTemplate/copy',
    method: 'post',
    data: copyRequest
  });
}

/**
 * 导入模板
 * @param {Array} templates - 模板列表
 * @returns {Promise} 操作结果
 */
export function importTemplates(templates) {
  return request({
    url: '/emailTemplate/import',
    method: 'post',
    data: templates
  });
}

/**
 * 导出模板
 * @param {string} emailType - 邮件类型
 * @param {string} language - 语言
 * @returns {Promise} 模板列表
 */
export function exportTemplates(emailType, language) {
  return request({
    url: '/emailTemplate/export',
    method: 'get',
    params: { emailType, language }
  });
}

// ==================== 邮件配置管理 API ====================

/**
 * 获取SMTP配置
 * @returns {Promise} SMTP配置数据
 */
export function getSmtpConfig() {
  return request({
    url: '/emailPushConfig/getSmtpConfig',
    method: 'get'
  });
}

/**
 * 更新SMTP配置
 * @param {Object} config - SMTP配置
 * @param {string} config.host - SMTP服务器地址
 * @param {string} config.port - SMTP端口
 * @param {string} config.username - 用户名
 * @param {string} config.password - 密码
 * @param {string} config.auth - 是否启用认证
 * @param {string} config.starttls - 是否启用STARTTLS
 * @param {string} config.ssl - 是否启用SSL
 * @returns {Promise} 更新结果
 */
export function updateSmtpConfig(config) {
  return request({
    url: '/emailPushConfig/updateSmtpConfig',
    method: 'post',
    data: config
  });
}

/**
 * 测试SMTP连接
 * @returns {Promise} 测试结果
 */
export function testSmtpConnection() {
  return request({
    url: '/emailPushConfig/testSmtpConnection',
    method: 'post'
  });
}

/**
 * 发送测试邮件
 * @param {string} testEmail - 测试邮箱地址
 * @returns {Promise} 测试结果
 */
export function sendTestEmail(testEmail) {
  return request({
    url: '/emailPush/testConfig',
    method: 'post',
    params: { testEmail }
  });
}

// ==================== 邮件类型和状态枚举 ====================

/**
 * 邮件类型枚举
 */
export const EMAIL_TYPES = {
  QUOTE_V1: 'QUOTE_V1',
  QUOTE_V2: 'QUOTE_V2', 
  QUOTE_V3: 'QUOTE_V3',
  QUOTE_V4: 'QUOTE_V4',
  QUOTE_V5: 'QUOTE_V5',
  ESTIMATE_Z: 'ESTIMATE_Z',
  ESTIMATE_ZZ: 'ESTIMATE_ZZ',
  P_VERSION: 'P_VERSION',
  UPDATE_NOTIFY: 'UPDATE_NOTIFY'
};

/**
 * 邮件类型显示名称
 */
export const EMAIL_TYPE_NAMES = {
  [EMAIL_TYPES.QUOTE_V1]: '推送報價1版',
  [EMAIL_TYPES.QUOTE_V2]: '推送報價2版',
  [EMAIL_TYPES.QUOTE_V3]: '推送報價3版',
  [EMAIL_TYPES.QUOTE_V4]: '推送報價4版',
  [EMAIL_TYPES.QUOTE_V5]: '推送報價5版',
  [EMAIL_TYPES.ESTIMATE_Z]: '推送預估Z版',
  [EMAIL_TYPES.ESTIMATE_ZZ]: '推送預估ZZ版',
  [EMAIL_TYPES.P_VERSION]: '推送P版',
  [EMAIL_TYPES.UPDATE_NOTIFY]: '更新版本邮件提醒'
};

/**
 * 发送状态枚举
 */
export const SEND_STATUS = {
  PENDING: 'PENDING',
  SENDING: 'SENDING',
  SUCCESS: 'SUCCESS',
  FAILED: 'FAILED',
  RETRY: 'RETRY'
};

/**
 * 发送状态显示名称
 */
export const SEND_STATUS_NAMES = {
  [SEND_STATUS.PENDING]: '待发送',
  [SEND_STATUS.SENDING]: '发送中',
  [SEND_STATUS.SUCCESS]: '发送成功',
  [SEND_STATUS.FAILED]: '发送失败',
  [SEND_STATUS.RETRY]: '重试中'
};

/**
 * 权限状态枚举
 */
export const PERMISSION_STATUS = {
  YES: 'Y',
  NO: 'N',
  PENDING: 'P'
};

/**
 * 权限状态显示名称
 */
export const PERMISSION_STATUS_NAMES = {
  [PERMISSION_STATUS.YES]: '有权限',
  [PERMISSION_STATUS.NO]: '无权限',
  [PERMISSION_STATUS.PENDING]: '待定'
};
