<template>
	<uni-icons :id="elId" class='load-ani' :style="aniStyle" @transitionend="onEnd" :type="iconType" :size="size" :color="color"></uni-icons>
</template>

<script>
	export default {
    name:'icon',
		props: {
			iconType: {
				type: String,
				default: 'loop'
			},
			size: {
				type: Number,
				default: 0
			},
			color: {
				type: String,
				default: '#333'
			}
		},
		data() {
			const elId = `Uni_${(Math.random() * 10e5).toInt().toString(36)}`
			return {
				elId: elId,
				element: null as UniElement | null,
				times: 0,
				aniStyle: '',
				deg: 3600000
			}
		},
		created() {
			this.times = 0

			// 需要延迟一些时间，否则动画不生效
			setTimeout(() => {
				this.aniStyle = 'transform:rotate(1deg);'
			}, 300)
		},
		mounted() {
			this.element = uni.getElementById(this.elId as string)
		},
		methods: {
			onEnd() {
				// 因为循环角度是不断增加，在增加10次以后需要重置，防止无限增加下去
				if (this.times == 10) {
					this.element!.style.setProperty('transform', 'rotate(0deg)')
					this.element!.style.setProperty('transition-duration', '1')
					this.times = 0
					return
				}

				this.times = this.times + 1

				const rotate = this.times * 360
				this.element!.style.setProperty('transform', 'rotate(' + rotate + 'deg)')
				this.element!.style.setProperty('transition-duration', '1000')
			}
		}
	}
</script>

<style>
	.load-ani {
		transition-property: transform;
		transition-duration: 0.1s;
		transition-timing-function: linear;
		transform: rotate(0deg);
	}
</style>
